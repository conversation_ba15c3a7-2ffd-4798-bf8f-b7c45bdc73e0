<template>
  <view class="sub-po-page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <u-icon name="arrow-left" size="20" color="#333"></u-icon>
        </view>
        <view class="navbar-title">代客预约</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content">
      <!-- 客户信息识别卡片 -->
      <view class="info-card identify-card">
        <view class="card-header">
          <view class="header-icon identify-icon">
            <u-icon name="scan" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">智能识别客户信息</text>
        </view>
        <view class="card-content">
          <view class="info-text">将客户信息（包括姓名、手机号、地址）粘贴到输入框后可自动识别出来</view>
          <view class="input-area">
            <textarea v-model="customerInfo" placeholder="请输入客户信息" :maxlength="200" @input="handleInput"></textarea>
            <view class="voice-btn" :class="{recording: isRecording}" @click="toggleVoiceInput">
              <u-icon :name="isRecording ? 'pause-circle-fill' : 'mic'" size="18" :color="isRecording ? '#fff' : '#666'"></u-icon>
            </view>
            <view class="counter">{{ inputLength }}/200</view>
          </view>
          <view class="action-buttons">
            <button class="parse-btn" @click="parseCustomerInfo">智能识别</button>
          </view>
        </view>
      </view>

      <!-- 基础信息卡片 -->
      <view class="info-card basic-info-card">
        <view class="card-header">
          <view class="header-icon customer-icon">
            <u-icon name="account" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">客户基础信息</text>
        </view>
        <view class="form-section">
          <view class="form-item">
            <view class="item-label required">联系人</view>
            <view class="item-content">
              <input type="text" v-model="parsedInfo.name" placeholder="请输入联系人姓名" placeholder-class="placeholder" />
            </view>
          </view>

          <view class="form-item">
            <view class="item-label required">联系方式</view>
            <view class="item-content">
              <input type="tel" v-model="parsedInfo.phone" placeholder="请输入电话号码" placeholder-class="placeholder" @input="handlePhoneInput" />
            </view>
          </view>

          <view class="form-item">
            <view class="item-label required">服务地址</view>
            <view class="item-content">
              <input type="text" v-model="parsedInfo.address" placeholder="请输入服务地址" placeholder-class="placeholder" @input="handleAddressInput" />
            </view>
          </view>

          <!-- 地图选点区域 -->
          <view class="form-item map-item" v-if="selectedLocation.latitude && selectedLocation.longitude">
            <view class="item-label">位置信息</view>
            <view class="item-content">
              <view class="map-container" @click="openMapSelector">
                <map
                  class="location-map"
                  :latitude="selectedLocation.latitude"
                  :longitude="selectedLocation.longitude"
                  :markers="mapMarkers"
                  :show-location="true"
                  enable-zoom="false"
                  enable-scroll="false"
                  enable-rotate="false"
                  enable-overlooking="false"
                  enable-traffic="false"
                ></map>
                <view class="map-overlay">
                  <view class="location-info">
                    <text class="location-address">{{ selectedLocation.address || '位置信息' }}</text>
                    <text class="location-coords">{{ selectedLocation.latitude.toFixed(4) }}, {{ selectedLocation.longitude.toFixed(4) }}</text>
                  </view>
                  <view class="map-tip">
                    <u-icon name="eye" size="12" color="#fff"></u-icon>
                    <text class="tip-text">点击查看大图</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <view class="form-item">
            <view class="item-label">门牌号</view>
            <view class="item-content">
              <input type="text" v-model="doorNumber" placeholder="请输入详细门牌号" placeholder-class="placeholder" />
            </view>
          </view>
        </view>
      </view>

      <!-- 服务信息卡片 -->
      <view class="info-card service-info-card">
        <view class="card-header">
          <view class="header-icon service-icon">
            <u-icon name="setting" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">服务信息</text>
        </view>
        <view class="form-section">
          <!-- 选择项目 -->
          <view class="form-item clickable" @click="handleSelectProject">
            <view class="item-label required">选择项目</view>
            <view class="item-content">
              <view class="selected-project" v-if="selectedSku">
                <view class="project-info">
                  <text class="project-name">{{ selectedSku.skuName }}</text>
                  <text class="project-price">¥{{ selectedSku.skuPrice }}</text>
                </view>
              </view>
              <view class="placeholder-text" v-else>请选择服务项目</view>
              <u-icon name="arrow-right" size="16" color="#999"></u-icon>
            </view>
          </view>

          <!-- 购买金额 -->
          <view class="form-item">
            <view class="item-label required">购买金额</view>
            <view class="item-content">
              <input type="number" v-model="amount" placeholder="请输入购买金额" placeholder-class="placeholder" />
              <text class="unit">元</text>
            </view>
          </view>

          <!-- 服务日期 -->
          <view class="form-item clickable" @click="handleSelectDate">
            <view class="item-label required">服务日期</view>
            <view class="item-content">
              <text class="selected-value" v-if="serviceDate">{{ serviceDate }}</text>
              <text class="placeholder-text" v-else>请选择服务日期</text>
              <u-icon name="arrow-right" size="16" color="#999"></u-icon>
            </view>
          </view>

          <!-- 服务时间 -->
          <view class="form-item clickable" @click="handleSelectTime">
            <view class="item-label required">服务时间</view>
            <view class="item-content">
              <text class="selected-value" v-if="serviceTime">{{ serviceTime }}</text>
              <text class="placeholder-text" v-else>请选择服务时间</text>
              <u-icon name="arrow-right" size="16" color="#999"></u-icon>
            </view>
          </view>

          <!-- 服务提醒 -->
          <view class="form-item">
            <view class="item-label">服务提醒</view>
            <view class="item-content">
              <textarea v-model="serviceNotice" placeholder="请输入服务提醒内容" placeholder-class="placeholder" :maxlength="200"></textarea>
            </view>
          </view>
        </view>
      </view>

      <!-- 扩展信息卡片 -->
      <view class="info-card extended-info-card">
        <view class="card-header">
          <view class="header-icon extended-icon">
            <u-icon name="list" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">扩展信息</text>
          <text class="card-subtitle">选填项目</text>
        </view>
        <view class="form-section">
          <!-- 动态标签区域 -->
          <view class="tags-container">
            <view 
              class="tag-item" 
              :class="{ active: selectedTags[tag] }" 
              v-for="tag in availableTags" 
              :key="tag"
              @click="toggleTag(tag)"
            >
              <text>{{ tag }}</text>
            </view>
          </view>

          <!-- 动态表单项 -->
          <view class="dynamic-form-items">
            <!-- 销售归属 -->
            <view class="form-item clickable" v-if="selectedTags['销售归属']" @click="handleSelectSalesAttribution">
              <view class="item-label">销售归属</view>
              <view class="item-content">
                <text class="selected-value" v-if="salesAttribution">{{ salesAttribution }}</text>
                <text class="placeholder-text" v-else>请选择销售归属</text>
                <u-icon name="arrow-right" size="16" color="#999"></u-icon>
              </view>
            </view>

            <!-- 销售提成 -->
            <view class="form-item" v-if="selectedTags['销售归属']">
              <view class="item-label">销售提成</view>
              <view class="item-content">
                <input type="number" v-model="salesCommission" placeholder="请输入销售提成" placeholder-class="placeholder" />
                <text class="unit">元</text>
              </view>
            </view>

            <!-- 其他动态字段 -->
            <view class="form-item" v-if="selectedTags['渠道编号']">
              <view class="item-label">渠道编号</view>
              <view class="item-content">
                <input type="text" v-model="channelCode" placeholder="请输入渠道编号" placeholder-class="placeholder" />
              </view>
            </view>

            <view class="form-item" v-if="selectedTags['订单来源']">
              <view class="item-label">订单来源</view>
              <view class="item-content">
                <input type="text" v-model="orderSource" placeholder="请输入订单来源" placeholder-class="placeholder" />
              </view>
            </view>

            <view class="form-item" v-if="selectedTags['面积']">
              <view class="item-label">面积</view>
              <view class="item-content">
                <input type="text" v-model="area" placeholder="请输入面积" placeholder-class="placeholder" />
                <text class="unit">㎡</text>
              </view>
            </view>

            <view class="form-item" v-if="selectedTags['户型']">
              <view class="item-label">户型</view>
              <view class="item-content">
                <input type="text" v-model="houseType" placeholder="请输入户型" placeholder-class="placeholder" />
              </view>
            </view>

            <view class="form-item" v-if="selectedTags['客户备注']">
              <view class="item-label">客户备注</view>
              <view class="item-content">
                <textarea v-model="customerNote" placeholder="请输入客户备注" placeholder-class="placeholder" :maxlength="200"></textarea>
              </view>
            </view>

            <view class="form-item" v-if="selectedTags['售后备注']">
              <view class="item-label">售后备注</view>
              <view class="item-content">
                <textarea v-model="afterSalesNote" placeholder="请输入售后备注" placeholder-class="placeholder" :maxlength="200"></textarea>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作区域 -->
    <view class="bottom-actions">
      <view class="submit-btn" @click="handleSubmit" :class="{ 'disabled': submitting }">
        <u-icon v-if="!submitting" name="checkmark" size="18" color="#fff"></u-icon>
        <u-loading-icon v-else mode="spinner" color="#fff" size="18"></u-loading-icon>
        <text v-if="submitting">提交中...</text>
        <text v-else>确认下单</text>
      </view>
    </view>

    <!-- 日期选择器 -->
    <u-datetime-picker
      :show="showDatePicker"
      mode="date"
      :min-date="minDateTimestamp"
      @confirm="confirmDate"
      @cancel="showDatePicker = false"
    ></u-datetime-picker>

    <!-- 时间选择器 -->
    <u-picker
      :show="showTimePicker"
      :columns="[timeOptions]"
      @confirm="confirmTime"
      @cancel="showTimePicker = false"
      keyName="label"
    ></u-picker>

    <!-- 员工选择组件 -->
    <staff-selector
      :show.sync="showStaffSelector"
      mode="single"
      title="选择销售归属"
      subtitle="请选择业绩归属销售人员"
      confirmText="确定"
      :showSearch="true"
      searchPlaceholder="搜索员工姓名或手机号"
      staffType="all"
      @confirm="handleStaffSelected"
      @cancel="handleStaffSelectorCancel"
    />
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { uploadFileToPublic } from '@/utlis/common.js'
import { voiceRecognizeByUrl, geocodeAddress } from '@/api/common.js'
import { get } from '@/utlis/require.js'
import StaffSelector from '../../home/<USER>/staff-selector/staff-selector.vue'

export default {
  components: {
    StaffSelector
  },

  computed: {
    ...mapState(['StatusBar', 'storeInfo', 'staffInfo']),

    // 计算销售提成
    calculatedSalesCommission() {
      if (!this.selectedSku || !this.amount) return 0;
      
      const amount = parseFloat(this.amount) || 0;
      const commissionRate = parseFloat(this.selectedSku.skuCommission) || 0;
      const commissionType = this.selectedSku.skuCommissionType || 1;
      
      if (commissionType === 1) {
        // 按比例计算
        return amount * (commissionRate / 100);
      } else {
        // 固定金额
        return commissionRate;
      }
    },

    // 地图标记
    mapMarkers() {
      if (!this.selectedLocation.latitude || !this.selectedLocation.longitude) {
        return [];
      }
      return [{
        id: 1,
        latitude: this.selectedLocation.latitude,
        longitude: this.selectedLocation.longitude,
        iconPath: '/static/img/map-marker.png',
        width: 30,
        height: 30
      }];
    }
  },

  data() {
    return {
      customerInfo: '', // 客户信息输入
      inputLength: 0, // 输入长度计数
      amount: '', // 购买金额
      serviceNotice: '', // 服务提醒
      doorNumber: '', // 门牌号
      // 支付方式相关
      paymentType: 'cash', // 支付方式：cash-现金支付
      // 语音识别相关（百度语音识别）
      recorderManager: null, // 原生录音管理器
      isRecording: false, // 是否正在录音
      recordingTimer: null, // 录音计时器
      recordingDuration: 0, // 录音时长（秒）
      maxRecordingDuration: 30, // 最大录音时长（秒）
      minRecordingDuration: 2, // 最小录音时长（秒）
      recordingFilePath: '', // 录音文件路径
      // 解析后的客户信息
      parsedInfo: {
        name: '', // 客户姓名
        phone: '', // 客户电话
        address: '', // 客户地址
      },
      // 地图选点信息
      selectedLocation: {
        latitude: 0,
        longitude: 0,
        address: '',
        name: ''
      },
      // 动态标签系统
      availableTags: ['销售归属', '渠道编号', '订单来源', '面积', '户型', '客户备注', '售后备注'],
      selectedTags: {}, // 选中的标签
      // 额外字段
      customerNote: '', // 客户备注
      salesAttribution: '', // 销售归属（显示名称）
      selectedSalesStaff: null, // 选中的销售员工对象
      salesCommission: '', // 销售提成
      channelCode: '', // 渠道编号
      orderSource: '', // 订单来源
      afterSalesNote: '', // 售后备注
      area: '', // 面积
      houseType: '', // 户型
      // 员工选择相关
      showStaffSelector: false, // 显示员工选择器
      // 选中的SKU信息
      selectedSku: null,
      // 服务日期时间
      serviceDate: '', // 服务日期
      serviceTime: '', // 服务时间
      tempServiceTime: '', // 临时服务时间（用于picker）
      showDatePicker: false, // 显示日期选择器
      showTimePicker: false, // 显示时间选择器
      minDateTimestamp: new Date().getTime(), // 最小日期时间戳
      // 时间选项（00:00-23:00，每小时一个选项）
      timeOptions: [
        { label: '00:00', value: '00:00' },
        { label: '01:00', value: '01:00' },
        { label: '02:00', value: '02:00' },
        { label: '03:00', value: '03:00' },
        { label: '04:00', value: '04:00' },
        { label: '05:00', value: '05:00' },
        { label: '06:00', value: '06:00' },
        { label: '07:00', value: '07:00' },
        { label: '08:00', value: '08:00' },
        { label: '09:00', value: '09:00' },
        { label: '10:00', value: '10:00' },
        { label: '11:00', value: '11:00' },
        { label: '12:00', value: '12:00' },
        { label: '13:00', value: '13:00' },
        { label: '14:00', value: '14:00' },
        { label: '15:00', value: '15:00' },
        { label: '16:00', value: '16:00' },
        { label: '17:00', value: '17:00' },
        { label: '18:00', value: '18:00' },
        { label: '19:00', value: '19:00' },
        { label: '20:00', value: '20:00' },
        { label: '21:00', value: '21:00' },
        { label: '22:00', value: '22:00' },
        { label: '23:00', value: '23:00' }
      ],
      // 提交状态
      submitting: false,
      // 门店信息
      storeInfo: null, // 门店信息
      storeBusinessHours: '08:00-18:00', // 门店营业时间，默认值
    };
  },

  onReady() {
    this.initRecorder();
  },

  onLoad(options) {
    console.log('员工端代客预约页面接收到的参数:', options);
    
    // 如果传入了clientId，获取客户信息并自动填入
    if (options.clientId) {
      this.loadClientInfo(options.clientId);
    }
  },

  onShow() {
    // 监听SKU选择事件（备用机制）
    uni.$on('skuSelected', this.handleSkuSelected);

    // 检查存储中的SKU数据（主要机制）
    this.checkStoredSku();

    // 获取门店信息，用于设置服务时间选项
    this.loadStoreInfo();
  },

  onHide() {
    // 移除事件监听
    uni.$off('skuSelected', this.handleSkuSelected);
  },

  onUnload() {
    // 清理录音资源
    if (this.recorderManager) {
      this.recorderManager.stop();
    }
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 加载客户信息并自动填入手机号
    async loadClientInfo(clientId) {
      try {
        console.log('开始获取客户信息, clientId:', clientId);

        // 先尝试从客户列表API获取信息，避免调用不存在的详情接口
        const clientInfo = await this.getClientFromList(clientId);

        if (clientInfo) {
          // 自动填入手机号
          this.parsedInfo.phone = clientInfo.mobile || '';

          // 如果存在姓名，也自动填入
          if (clientInfo.name) {
            this.parsedInfo.name = clientInfo.name;
          }

          // 根据填入的信息显示提示
          let message = '客户手机号已自动填入';
          if (this.parsedInfo.name) {
            message = '客户信息已自动填入';
          }

          uni.showToast({
            title: message,
            icon: 'success',
            duration: 1500
          });

          console.log('客户信息填入成功:', {
            name: this.parsedInfo.name,
            phone: this.parsedInfo.phone
          });
        } else {
          throw new Error('未找到客户信息');
        }
      } catch (error) {
        console.error('获取客户信息失败:', error);
        uni.showToast({
          title: '获取客户信息失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 从客户列表中获取客户信息
    async getClientFromList(clientId) {
      try {
        // 这里应该调用客户列表API，然后从结果中找到对应的客户
        // 暂时返回null，实际项目中需要实现具体的API调用
        console.log('从客户列表获取客户信息，clientId:', clientId);
        return null;
      } catch (error) {
        console.error('从客户列表获取客户信息失败:', error);
        return null;
      }
    },

    // 处理输入事件
    handleInput(e) {
      this.inputLength = e.detail.value.length;
    },

    // 处理手机号输入
    handlePhoneInput(e) {
      // 可以在这里添加手机号格式验证
      console.log('手机号输入:', e.detail.value);
    },

    // 处理地址输入
    handleAddressInput(e) {
      // 可以在这里添加地址自动补全或验证
      console.log('地址输入:', e.detail.value);
    },

    // 初始化录音管理器
    initRecorder() {
      try {
        this.recorderManager = uni.getRecorderManager();

        this.recorderManager.onStart(() => {
          console.log('录音开始');
          this.isRecording = true;
          this.recordingDuration = 0;
          this.startRecordingTimer();
        });

        this.recorderManager.onStop((res) => {
          console.log('录音结束', res);
          this.isRecording = false;
          this.stopRecordingTimer();

          if (res.tempFilePath) {
            this.recordingFilePath = res.tempFilePath;
            this.processVoiceRecognition(res.tempFilePath);
          }
        });

        this.recorderManager.onError((err) => {
          console.error('录音错误:', err);
          this.isRecording = false;
          this.stopRecordingTimer();
          uni.showToast({
            title: '录音失败，请重试',
            icon: 'none'
          });
        });
      } catch (error) {
        console.error('初始化录音管理器失败:', error);
      }
    },

    // 切换语音输入
    toggleVoiceInput() {
      if (this.isRecording) {
        this.stopRecording();
      } else {
        this.startRecording();
      }
    },

    // 开始录音
    startRecording() {
      try {
        this.recorderManager.start({
          duration: this.maxRecordingDuration * 1000,
          sampleRate: 16000,
          numberOfChannels: 1,
          encodeBitRate: 96000,
          format: 'mp3'
        });
      } catch (error) {
        console.error('开始录音失败:', error);
        uni.showToast({
          title: '录音功能不可用',
          icon: 'none'
        });
      }
    },

    // 停止录音
    stopRecording() {
      try {
        if (this.recordingDuration < this.minRecordingDuration) {
          uni.showToast({
            title: `录音时长不能少于${this.minRecordingDuration}秒`,
            icon: 'none'
          });
          return;
        }
        this.recorderManager.stop();
      } catch (error) {
        console.error('停止录音失败:', error);
      }
    },

    // 开始录音计时
    startRecordingTimer() {
      this.recordingTimer = setInterval(() => {
        this.recordingDuration++;
        if (this.recordingDuration >= this.maxRecordingDuration) {
          this.stopRecording();
        }
      }, 1000);
    },

    // 停止录音计时
    stopRecordingTimer() {
      if (this.recordingTimer) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = null;
      }
    },

    // 处理语音识别
    async processVoiceRecognition(filePath) {
      try {
        uni.showLoading({
          title: '正在识别语音...',
          mask: true
        });

        // 上传音频文件
        const uploadResult = await uploadFileToPublic(filePath);

        if (!uploadResult || !uploadResult.url) {
          throw new Error('音频文件上传失败');
        }

        // 调用语音识别API
        const recognitionResult = await voiceRecognizeByUrl(uploadResult.url);

        uni.hideLoading();

        if (recognitionResult && recognitionResult.result && recognitionResult.result.length > 0) {
          // 获取识别结果
          const recognizedText = recognitionResult.result[0];

          // 将识别结果添加到输入框
          this.customerInfo = recognizedText;
          this.inputLength = recognizedText.length;

          uni.showToast({
            title: '语音识别成功',
            icon: 'success',
            duration: 1500
          });

          // 自动解析客户信息
          setTimeout(() => {
            this.parseCustomerInfo();
          }, 500);
        } else {
          throw new Error('语音识别失败，请重试');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('语音识别处理失败:', error);
        uni.showToast({
          title: error.message || '语音识别失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 智能解析客户信息
    parseCustomerInfo() {
      if (!this.customerInfo.trim()) {
        uni.showToast({
          title: '请先输入客户信息',
          icon: 'none'
        });
        return;
      }

      try {
        const text = this.customerInfo.trim();

        // 解析姓名（通常在开头）
        const nameMatch = text.match(/^([^\d\s]{2,4})/);
        if (nameMatch) {
          this.parsedInfo.name = nameMatch[1];
        }

        // 解析手机号
        const phoneMatch = text.match(/1[3-9]\d{9}/);
        if (phoneMatch) {
          this.parsedInfo.phone = phoneMatch[0];
        }

        // 解析地址（去除姓名和手机号后的剩余部分）
        let address = text;
        if (nameMatch) {
          address = address.replace(nameMatch[0], '');
        }
        if (phoneMatch) {
          address = address.replace(phoneMatch[0], '');
        }

        // 清理地址字符串
        address = address.replace(/[^\u4e00-\u9fa5\d\w\s]/g, '').trim();
        if (address.length > 5) {
          this.parsedInfo.address = address;

          // 自动进行地理编码
          this.geocodeAddressAutomatically(address);
        }

        // 显示解析结果
        const parsedFields = [];
        if (this.parsedInfo.name) parsedFields.push('姓名');
        if (this.parsedInfo.phone) parsedFields.push('手机号');
        if (this.parsedInfo.address) parsedFields.push('地址');

        if (parsedFields.length > 0) {
          uni.showToast({
            title: `已识别：${parsedFields.join('、')}`,
            icon: 'success',
            duration: 2000
          });
        } else {
          uni.showToast({
            title: '未识别到有效信息，请检查输入格式',
            icon: 'none',
            duration: 2000
          });
        }
      } catch (error) {
        console.error('解析客户信息失败:', error);
        uni.showToast({
          title: '解析失败，请手动输入',
          icon: 'none'
        });
      }
    },

    // 自动地理编码
    async geocodeAddressAutomatically(address) {
      if (!address || address.length < 5) {
        return;
      }

      try {
        // 显示地理编码进度
        uni.showLoading({
          title: '正在获取位置信息...',
          mask: true
        });

        const result = await this.callGeocodeAPI(address.trim());

        if (result && result.location) {
          // 更新位置信息
          this.selectedLocation = {
            latitude: result.location.lat,
            longitude: result.location.lng,
            address: result.formatted_addresses?.recommend || address,
            name: result.formatted_addresses?.recommend || address
          };

          // 更新地图标记
          this.updateMapMarkers();

          uni.hideLoading();

          // 显示成功提示
          uni.showToast({
            title: '位置信息获取成功',
            icon: 'success',
            duration: 1500
          });

          console.log('地理编码成功:', this.selectedLocation);
        } else {
          throw new Error('地理编码返回数据格式错误');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('自动地理编码失败:', error);
        // 自动地理编码失败不显示错误提示，用户可以手动重试
      }
    },

    // 调用后端地理编码API
    async callGeocodeAPI(address) {
      try {
        console.log('调用后端地理编码API:', address);

        // 调用后端地理编码接口
        const result = await geocodeAddress(address);

        console.log('后端地理编码API响应:', result);

        // 检查响应数据结构
        if (result && result.result && result.result.location) {
          // 返回与原来兼容的数据格式
          return {
            location: result.result.location,
            formatted_addresses: result.result.formatted_addresses,
            address_components: result.result.address_components
          };
        } else {
          throw new Error('地理编码返回数据格式错误');
        }
      } catch (error) {
        console.error('后端地理编码API调用失败:', error);

        // 处理错误信息
        let errorMessage = '地理编码失败';
        if (error.message) {
          errorMessage = error.message;
        } else if (error.msg) {
          errorMessage = error.msg;
        }

        throw new Error(errorMessage);
      }
    },

    // 更新地图标记
    updateMapMarkers() {
      // 地图标记通过计算属性自动更新
      console.log('地图标记已更新');
    },

    // 打开地图选择器
    openMapSelector() {
      const latitude = this.selectedLocation.latitude || 24.4797;
      const longitude = this.selectedLocation.longitude || 118.0819;

      uni.openLocation({
        latitude: latitude,
        longitude: longitude,
        name: this.selectedLocation.name || '服务地址',
        address: this.selectedLocation.address || '请选择具体位置',
        success: () => {
          console.log('打开地图成功');
        },
        fail: (error) => {
          console.error('打开地图失败:', error);
          uni.showToast({
            title: '打开地图失败',
            icon: 'none'
          });
        }
      });
    },

    // 切换标签
    toggleTag(tag) {
      this.$set(this.selectedTags, tag, !this.selectedTags[tag]);

      // 如果取消选择销售归属，清空相关数据
      if (tag === '销售归属' && !this.selectedTags[tag]) {
        this.salesAttribution = '';
        this.selectedSalesStaff = null;
        this.salesCommission = '';
      }
    },

    // 选择项目
    handleSelectProject() {
      // 跳转到产品列表页面
      uni.navigateTo({
        url: '/pages-home/product-list',
        success: () => {
          console.log('跳转到产品列表页面成功');
        },
        fail: (error) => {
          console.error('跳转到产品列表页面失败:', error);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },

    // 选择服务日期
    handleSelectDate() {
      this.showDatePicker = true;
    },

    // 选择服务时间
    handleSelectTime() {
      this.tempServiceTime = this.serviceTime || '09:00'; // 设置默认时间
      this.showTimePicker = true;
    },

    // 确认日期选择
    confirmDate(e) {
      const date = new Date(e.value);
      this.serviceDate = date.toISOString().split('T')[0]; // 格式化为 YYYY-MM-DD
      this.showDatePicker = false;

      console.log('选择的服务日期:', this.serviceDate);
    },

    // 确认时间选择
    confirmTime(e) {
      if (e.value && e.value.length > 0) {
        this.serviceTime = e.value[0].value;
      }
      this.showTimePicker = false;

      console.log('选择的服务时间:', this.serviceTime);
    },

    // 获取门店信息
    async loadStoreInfo() {
      try {
        // 员工端需要从当前选中的公司获取门店信息
        const selectedCompany = this.$store.getters.getCurrentSelectedCompany;
        if (selectedCompany) {
          this.storeInfo = selectedCompany;
          console.log('获取到门店信息:', this.storeInfo);
        } else {
          console.warn('未获取到门店信息');
        }
      } catch (error) {
        console.error('获取门店信息失败:', error);
      }
    },

    // 检查存储中的SKU数据
    checkStoredSku() {
      try {
        const storedSku = uni.getStorageSync('selectedSku');
        if (storedSku) {
          console.log('从存储中获取到选中的SKU:', storedSku);
          this.selectedSku = storedSku;

          // 自动填充购买金额
          this.amount = storedSku.skuPrice.toString();

          // 如果已经选择了销售归属，自动填充提成
          if (this.selectedTags['销售归属']) {
            this.updateSalesCommission();
          }

          // 清理存储数据
          uni.removeStorageSync('selectedSku');

          // 显示选择成功的提示
          uni.showToast({
            title: `已选择: ${storedSku.skuName}`,
            icon: 'success',
            duration: 2000
          });
        }
      } catch (error) {
        console.error('检查存储SKU数据失败:', error);
      }
    },

    // 处理SKU选择（备用机制）
    handleSkuSelected(sku) {
      console.log('通过事件接收到选中的SKU:', sku);

      // 如果当前没有选中SKU，则使用事件传递的SKU
      if (!this.selectedSku) {
        this.selectedSku = sku;

        // 自动填充购买金额
        this.amount = sku.skuPrice.toString();

        // 如果已经选择了销售归属，自动填充提成
        if (this.selectedTags['销售归属']) {
          this.updateSalesCommission();
        }

        // 显示选择成功的提示
        uni.showToast({
          title: `已选择: ${sku.skuName}`,
          icon: 'success',
          duration: 2000
        });
      }
    },

    // 截断文本
    truncateText(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    },

    // 选择销售归属
    handleSelectSalesAttribution() {
      console.log('打开员工选择器');
      this.showStaffSelector = true;
    },

    // 员工选择确认回调
    handleStaffSelected(staff) {
      console.log('选中员工:', staff);
      if (staff) {
        this.selectedSalesStaff = staff;
        this.salesAttribution = staff.real_name || staff.name || '未知';

        // 自动填充销售提成
        this.updateSalesCommission();

        uni.showToast({
          title: `已选择：${this.salesAttribution}`,
          icon: 'success',
          duration: 1500
        });
      }
      this.showStaffSelector = false;
    },

    // 员工选择取消回调
    handleStaffSelectorCancel() {
      console.log('取消员工选择');
      this.showStaffSelector = false;
    },

    // 更新销售提成
    updateSalesCommission() {
      // 只有在有SKU信息和金额时才自动填充
      if (this.selectedSku && this.amount) {
        const calculatedCommission = this.calculatedSalesCommission;
        if (calculatedCommission > 0) {
          this.salesCommission = calculatedCommission.toFixed(2);
          console.log('自动填充销售提成:', this.salesCommission);
        }
      }
    },

    // 表单验证
    validateForm() {
      // 检查客户姓名
      if (!this.parsedInfo.name || this.parsedInfo.name.trim().length < 2) {
        return { isValid: false, message: '请输入正确的客户姓名' };
      }

      // 检查客户手机号
      if (!this.parsedInfo.phone || !/^1[3-9]\d{9}$/.test(this.parsedInfo.phone)) {
        return { isValid: false, message: '请输入正确的手机号码' };
      }

      // 检查服务地址
      if (!this.parsedInfo.address || this.parsedInfo.address.trim().length < 5) {
        return { isValid: false, message: '请输入详细的服务地址' };
      }

      // 检查选择的项目
      if (!this.selectedSku) {
        return { isValid: false, message: '请选择服务项目' };
      }

      // 检查购买金额
      if (!this.amount || parseFloat(this.amount) <= 0) {
        return { isValid: false, message: '请输入正确的购买金额' };
      }

      // 检查服务日期
      if (!this.serviceDate) {
        return { isValid: false, message: '请选择服务日期' };
      }

      // 检查服务时间
      if (!this.serviceTime) {
        return { isValid: false, message: '请选择服务时间' };
      }

      // 检查经纬度（如果有地址但没有经纬度，给出提示）
      if (!this.selectedLocation.latitude || !this.selectedLocation.longitude) {
        return { isValid: false, message: '请重新选择服务地址以获取准确位置' };
      }

      return { isValid: true, message: '' };
    },

    // 准备提交数据
    prepareSubmitData() {
      // 构建完整的服务地址
      let fullAddress = this.parsedInfo.address;
      if (this.doorNumber) {
        fullAddress += ` ${this.doorNumber}`;
      }

      // 验证必需的数据
      if (!this.selectedSku || !this.selectedSku.productUuid) {
        throw new Error('请先选择服务产品');
      }

      // 使用员工专用代客下单接口格式构建数据
      const submitData = {
        // 1. 基础订单信息
        order_info: {
          amount: parseFloat(this.amount) || 0, // 确保为数字类型
          buy_num: 1.0, // 确保为数字类型
          pay_type: '106', // 现金支付
          service_date: this.serviceDate, // 格式：YYYY-MM-DD
          service_time: this.serviceTime  // 格式：HH:MM
        },

        // 2. 客户信息
        customer_info: {
          name: this.parsedInfo.name,
          phone: this.parsedInfo.phone,
          original_input: this.customerInfo
        },

        // 3. 服务地址信息
        address_info: {
          address: fullAddress,
          door_number: this.doorNumber || '',
          longitude: parseFloat(this.selectedLocation.longitude) || 0, // 确保为数字类型
          latitude: parseFloat(this.selectedLocation.latitude) || 0,   // 确保为数字类型
          city: "厦门市",
          province: "福建省",
          district: ""
        },

        // 4. 产品SKU信息
        product_info: {
          product_uuid: this.selectedSku.productUuid,
          sku_id: this.selectedSku.skuId,
          sku_name: this.selectedSku.skuName,
          sku_price: parseFloat(this.selectedSku.skuPrice) || 0, // 确保为数字类型
          sku_commission: parseFloat(this.selectedSku.skuCommission) || 0, // 确保为数字类型
          sku_commission_type: this.selectedSku.skuCommissionType || 1
        },

        // 5. 备注信息
        remark_info: {
          service_notice: this.serviceNotice,
          customer_note: this.selectedTags['客户备注'] ? this.customerNote : '',
          after_sales_note: this.selectedTags['售后备注'] ? this.afterSalesNote : ''
        },

        // 6. 扩展业务信息
        business_info: {
          sales_attribution: this.selectedTags['销售归属'] ? this.salesAttribution : '',
          sales_attribution_staff_id: this.selectedTags['销售归属'] && this.selectedSalesStaff ? this.selectedSalesStaff.uuid : '',
          sales_attribution_staff_name: this.selectedTags['销售归属'] && this.selectedSalesStaff ? (this.selectedSalesStaff.real_name || this.selectedSalesStaff.name) : '',
          sales_commission: this.selectedTags['销售归属'] && this.salesCommission ? this.salesCommission : '',
          channel_code: this.selectedTags['渠道编号'] ? this.channelCode : '',
          order_source: this.selectedTags['订单来源'] ? this.orderSource : '',
          area: this.selectedTags['面积'] ? this.area : '',
          house_type: this.selectedTags['户型'] ? this.houseType : ''
        }
      };

      return submitData;
    },

    // 提交表单
    async handleSubmit() {
      // 防止重复提交
      if (this.submitting) {
        return;
      }

      // 检查必填项
      const validationResult = this.validateForm();
      if (!validationResult.isValid) {
        uni.showToast({
          title: validationResult.message,
          icon: 'none',
          duration: 2000
        });
        return;
      }

      try {
        this.submitting = true;

        // 显示加载提示
        uni.showLoading({
          title: '正在提交订单...',
          mask: true
        });

        // 准备提交数据
        const submitData = this.prepareSubmitData();

        console.log('员工端代客预约提交数据:', submitData);

        // 调用员工专用下单接口
        const result = await this.submitStaffOrder(submitData);

        uni.hideLoading();

        // 显示成功提示
        uni.showToast({
          title: '订单创建成功',
          icon: 'success',
          duration: 2000
        });

        // 延迟跳转到订单详情或返回上一页
        setTimeout(() => {
          if (result && result.order_number) {
            // 如果有订单号，跳转到订单详情
            uni.redirectTo({
              url: `/pages-dispatch/order-detail-new?orderNumber=${result.order_number}`
            });
          } else {
            // 否则返回上一页
            uni.navigateBack();
          }
        }, 1500);

      } catch (error) {
        uni.hideLoading();
        console.error('员工端代客预约提交失败:', error);

        uni.showToast({
          title: error.message || '订单创建失败，请重试',
          icon: 'none',
          duration: 3000
        });
      } finally {
        this.submitting = false;
      }
    },

    // 提交员工订单到后端
    async submitStaffOrder(orderData) {
      try {
        // 检查员工登录状态
        const staffToken = this.$store.state.staffToken || '';
        if (!staffToken) {
          throw new Error('员工未登录，请先登录');
        }

        // 手动将对象转换为JSON字符串
        const jsonData = JSON.stringify(orderData);
        console.log('发送的JSON数据:', jsonData);

        // 使用统一的请求工具发送POST请求到员工专用接口
        const result = await this.$post('/api/v1/staff-order/proxy-create', jsonData, {
          contentType: 'application/json'
        });

        console.log('员工代客下单接口响应:', result);
        return result;
      } catch (error) {
        console.error('员工代客下单接口请求失败:', error);

        // 根据错误类型进行处理
        if (error.code) {
          const errorMsg = this.handleOrderError(error.code, error.message);
          throw new Error(errorMsg);
        } else {
          throw new Error(`网络请求失败: ${error.message || '未知错误'}`);
        }
      }
    },

    // 处理订单错误
    handleOrderError(code, message) {
      const errorMap = {
        1001: '参数验证失败，请检查输入信息',
        1002: '客户信息不完整',
        1003: '地址信息不完整',
        1004: '产品信息不存在',
        1005: '门店信息不存在',
        1006: '员工权限不足',
        1007: '订单创建失败',
        1008: '支付处理失败',
        1009: '数据处理异常'
      };

      return errorMap[code] || message || '订单处理失败';
    }
  }
};
</script>

<style lang="scss" scoped>
// 员工端代客预约页面样式
.sub-po-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
  padding-bottom: 120rpx;
}

// 自定义导航栏
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  border-bottom: 1px solid #eee;
  padding-top: var(--status-bar-height);
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.navbar-right {
  width: 80rpx;
}

// 内容区域
.content {
  padding-top: calc(var(--status-bar-height) + 88rpx + 32rpx);
  padding-left: 32rpx;
  padding-right: 32rpx;
}

// 信息卡片样式
.info-card {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.header-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  background: rgba(255, 255, 255, 0.2);
}

.identify-icon {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.customer-icon {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.service-icon {
  background: linear-gradient(135deg, #45b7d1 0%, #96c93d 100%);
}

.extended-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  flex: 1;
}

.card-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
  margin-left: 16rpx;
}

.card-content {
  padding: 32rpx;
}

// 智能识别卡片特殊样式
.identify-card .info-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.input-area {
  position: relative;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.input-area textarea {
  width: 100%;
  min-height: 120rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
}

.voice-btn {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.voice-btn.recording {
  background: #ff6b6b;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.counter {
  position: absolute;
  bottom: 16rpx;
  right: 24rpx;
  font-size: 24rpx;
  color: #999;
}

.action-buttons {
  display: flex;
  justify-content: center;
}

.parse-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 48rpx;
  font-size: 28rpx;
  font-weight: 500;
}

// 表单样式
.form-section {
  padding: 0;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.form-item.clickable {
  cursor: pointer;
}

.item-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex-shrink: 0;
}

.item-label.required::after {
  content: '*';
  color: #ff6b6b;
  margin-left: 8rpx;
}

.item-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-content input,
.item-content textarea {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.placeholder {
  color: #999 !important;
}

.unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

.selected-value {
  font-size: 28rpx;
  color: #333;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
}

// 选中项目样式
.selected-project {
  flex: 1;
}

.project-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.project-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.project-price {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: 600;
}

// 地图样式
.map-item {
  flex-direction: column;
  align-items: flex-start;
}

.map-container {
  width: 100%;
  height: 300rpx;
  border-radius: 16rpx;
  overflow: hidden;
  position: relative;
  margin-top: 16rpx;
}

.location-map {
  width: 100%;
  height: 100%;
}

.map-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 32rpx 24rpx 24rpx;
  color: #fff;
}

.location-info {
  margin-bottom: 16rpx;
}

.location-address {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.location-coords {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.map-tip {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  opacity: 0.9;
}

.tip-text {
  margin-left: 8rpx;
}

// 标签系统样式
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.tag-item {
  padding: 16rpx 32rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 50rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.tag-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: #fff;
}

.dynamic-form-items {
  margin-top: 16rpx;
}

// 底部操作区域
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 32rpx;
  border-top: 1px solid #eee;
  z-index: 999;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.submit-btn.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.submit-btn text {
  margin-left: 16rpx;
}
</style>
