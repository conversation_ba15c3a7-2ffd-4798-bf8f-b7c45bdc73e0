<template>
  <u-popup :show="show" mode="bottom" @close="handleClose" round="20">
    <view class="staff-selector">
      <!-- 头部 -->
      <view class="selector-header">
        <view class="header-left" @click="handleCancel">
          <text class="cancel-text">取消</text>
        </view>
        <view class="header-center">
          <text class="title">{{ title }}</text>
          <text class="subtitle" v-if="subtitle">{{ subtitle }}</text>
        </view>
        <view class="header-right" @click="handleConfirm">
          <text class="confirm-text">{{ confirmText }}</text>
        </view>
      </view>

      <!-- 搜索框 -->
      <view class="search-container" v-if="showSearch">
        <view class="search-box">
          <u-icon name="search" size="18" color="#999"></u-icon>
          <input
            type="text"
            :placeholder="searchPlaceholder"
            v-model="searchKeyword"
            @input="handleSearchInput"
            class="search-input"
          />
          <view class="search-clear" v-if="searchKeyword" @click="clearSearch">
            <u-icon name="close-circle" size="16" color="#999"></u-icon>
          </view>
        </view>
      </view>

      <!-- 员工列表 -->
      <scroll-view scroll-y class="staff-list" @scrolltolower="loadMoreStaff">
        <view
          class="staff-item"
          v-for="staff in filteredStaffList"
          :key="staff.uuid || staff.id"
          @click="handleStaffSelect(staff)"
        >
          <view class="staff-avatar">
            <image
              :src="staff.avatar || defaultAvatar"
              mode="aspectFill"
              class="avatar-image"
              @error="onAvatarError"
            ></image>
          </view>
          <view class="staff-info">
            <view class="staff-name">{{ staff.real_name || staff.name || '未知' }}</view>
            <view class="staff-details">
              <text class="staff-phone">{{ staff.mobile || staff.phone || '暂无电话' }}</text>
              <text class="staff-role" v-if="staff.role_name">{{ staff.role_name }}</text>
            </view>
          </view>
          <view class="staff-select">
            <view
              class="select-radio"
              :class="{ active: isSelected(staff) }"
            >
              <u-icon v-if="isSelected(staff)" name="checkmark" size="16" color="#fff"></u-icon>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" v-if="loading">
          <u-loading-icon mode="spinner" size="24"></u-loading-icon>
          <text class="load-text">加载中...</text>
        </view>

        <!-- 无更多数据 -->
        <view class="no-more" v-if="noMoreData && filteredStaffList.length > 0">
          <text>没有更多数据了</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && filteredStaffList.length === 0">
          <u-icon name="account" size="48" color="#ccc"></u-icon>
          <text class="empty-text">暂无员工数据</text>
        </view>
      </scroll-view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: 'StaffSelector',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'single' // single: 单选, multiple: 多选
    },
    title: {
      type: String,
      default: '选择员工'
    },
    subtitle: {
      type: String,
      default: ''
    },
    confirmText: {
      type: String,
      default: '确定'
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    searchPlaceholder: {
      type: String,
      default: '搜索员工姓名或手机号'
    },
    staffType: {
      type: String,
      default: 'all' // all: 所有员工, service: 服务员工
    }
  },

  data() {
    return {
      staffList: [],
      selectedStaff: null,
      selectedStaffList: [],
      searchKeyword: '',
      loading: false,
      noMoreData: false,
      currentPage: 1,
      pageSize: 20,
      defaultAvatar: 'https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/img/logo.png'
    };
  },

  computed: {
    filteredStaffList() {
      if (!this.searchKeyword) {
        return this.staffList;
      }
      
      const keyword = this.searchKeyword.toLowerCase();
      return this.staffList.filter(staff => {
        const name = (staff.real_name || staff.name || '').toLowerCase();
        const phone = (staff.mobile || staff.phone || '').toLowerCase();
        return name.includes(keyword) || phone.includes(keyword);
      });
    }
  },

  watch: {
    show(newVal) {
      if (newVal) {
        this.loadStaffList();
      } else {
        this.resetData();
      }
    }
  },

  methods: {
    // 加载员工列表
    async loadStaffList(refresh = false) {
      if (this.loading) return;

      try {
        this.loading = true;

        if (refresh) {
          this.currentPage = 1;
          this.noMoreData = false;
        }

        // 这里应该调用员工列表API
        // 暂时使用模拟数据
        const mockStaffList = [
          {
            uuid: '1',
            id: '1',
            real_name: '张三',
            mobile: '13800138001',
            avatar: '',
            role_name: '服务员'
          },
          {
            uuid: '2',
            id: '2',
            real_name: '李四',
            mobile: '13800138002',
            avatar: '',
            role_name: '销售员'
          }
        ];

        if (refresh || this.currentPage === 1) {
          this.staffList = mockStaffList;
        } else {
          this.staffList = [...this.staffList, ...mockStaffList];
        }

        this.noMoreData = mockStaffList.length < this.pageSize;
        this.currentPage++;

      } catch (error) {
        console.error('加载员工列表失败:', error);
        uni.showToast({
          title: '加载员工列表失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载更多员工
    loadMoreStaff() {
      if (!this.noMoreData && !this.loading) {
        this.loadStaffList();
      }
    },

    // 处理员工选择
    handleStaffSelect(staff) {
      if (this.mode === 'single') {
        this.selectedStaff = staff;
      } else {
        const index = this.selectedStaffList.findIndex(s => 
          (s.uuid && s.uuid === staff.uuid) || (s.id && s.id === staff.id)
        );
        
        if (index > -1) {
          this.selectedStaffList.splice(index, 1);
        } else {
          this.selectedStaffList.push(staff);
        }
      }
    },

    // 判断是否已选择
    isSelected(staff) {
      if (this.mode === 'single') {
        return this.selectedStaff && 
               ((this.selectedStaff.uuid && this.selectedStaff.uuid === staff.uuid) ||
                (this.selectedStaff.id && this.selectedStaff.id === staff.id));
      } else {
        return this.selectedStaffList.some(s => 
          (s.uuid && s.uuid === staff.uuid) || (s.id && s.id === staff.id)
        );
      }
    },

    // 搜索输入处理
    handleSearchInput(e) {
      this.searchKeyword = e.detail.value;
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = '';
    },

    // 头像加载错误处理
    onAvatarError(e) {
      e.target.src = this.defaultAvatar;
    },

    // 确认选择
    handleConfirm() {
      if (this.mode === 'single') {
        if (this.selectedStaff) {
          this.$emit('confirm', this.selectedStaff);
        } else {
          uni.showToast({
            title: '请选择员工',
            icon: 'none'
          });
          return;
        }
      } else {
        this.$emit('confirm', this.selectedStaffList);
      }
      
      this.handleClose();
    },

    // 取消选择
    handleCancel() {
      this.$emit('cancel');
      this.handleClose();
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('update:show', false);
    },

    // 重置数据
    resetData() {
      this.selectedStaff = null;
      this.selectedStaffList = [];
      this.searchKeyword = '';
      this.currentPage = 1;
      this.noMoreData = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.staff-selector {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.header-left,
.header-right {
  width: 120rpx;
}

.cancel-text {
  color: #999;
  font-size: 28rpx;
}

.confirm-text {
  color: #007aff;
  font-size: 28rpx;
  font-weight: 500;
  text-align: right;
}

.header-center {
  flex: 1;
  text-align: center;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
}

.subtitle {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}

.search-container {
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 16rpx 24rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  margin-left: 16rpx;
  background: transparent;
  border: none;
  outline: none;
}

.search-clear {
  margin-left: 16rpx;
}

.staff-list {
  flex: 1;
  padding: 0 32rpx;
}

.staff-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.staff-item:last-child {
  border-bottom: none;
}

.staff-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.staff-info {
  flex: 1;
}

.staff-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.staff-details {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.staff-phone {
  font-size: 24rpx;
  color: #666;
}

.staff-role {
  font-size: 22rpx;
  color: #999;
  background: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.staff-select {
  margin-left: 24rpx;
  flex-shrink: 0;
}

.select-radio {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.select-radio.active {
  background: #007aff;
  border-color: #007aff;
}

.load-more,
.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  color: #999;
  font-size: 24rpx;
}

.load-text {
  margin-left: 16rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  color: #999;
}

.empty-text {
  margin-top: 24rpx;
  font-size: 28rpx;
}
</style>
