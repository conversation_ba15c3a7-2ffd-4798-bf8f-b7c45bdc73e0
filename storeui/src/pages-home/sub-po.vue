<template>
  <view class="page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 现代化头部 -->
    <view class="header-section">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 顶部导航 -->
        <view class="top-nav">
          <view class="nav-left" @click="handleBack">
            <view class="back-btn">
              <u-icon name="arrow-left" color="#fff" size="20"></u-icon>
            </view>
          </view>
          <view class="nav-center">
            <text class="nav-title">{{ isStaffMode ? '代客预约' : '代客下单' }}</text>
          </view>
          <view class="nav-right"></view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content">
      <!-- 客户信息识别卡片 -->
      <view class="info-card identify-card">
        <view class="card-header">
          <view class="header-icon identify-icon">
            <u-icon name="scan" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">智能识别客户信息</text>
        </view>
        <view class="card-content">
          <view class="info-text">将客户信息（包括姓名、手机号、地址）粘贴到输入框后可自动识别出来</view>
          <view class="input-area">
            <textarea v-model="customerInfo" placeholder="请输入客户信息" :maxlength="200" @input="handleInput"></textarea>
            <view class="voice-btn" :class="{recording: isRecording}" @click="toggleVoiceInput">
              <u-icon :name="isRecording ? 'pause-circle-fill' : 'mic'" size="18" :color="isRecording ? '#fff' : '#666'"></u-icon>
            </view>
            <view class="counter">{{ inputLength }}/200</view>
          </view>
          <view class="identify-btn" @click="handleIdentify">
            <u-icon name="scan" size="16" color="#fff"></u-icon>
            <text>智能识别</text>
          </view>
        </view>
      </view>

      <!-- 基础信息卡片 -->
      <view class="info-card basic-info-card">
        <view class="card-header">
          <view class="header-icon customer-icon">
            <u-icon name="account" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">客户基础信息</text>
        </view>
        <view class="form-section">
          <view class="form-item">
            <view class="item-label required">联系人</view>
            <view class="item-content">
              <input type="text" v-model="parsedInfo.name" placeholder="请输入联系人姓名" placeholder-class="placeholder" />
            </view>
          </view>

          <view class="form-item">
            <view class="item-label required">联系方式</view>
            <view class="item-content">
              <input type="tel" v-model="parsedInfo.phone" placeholder="请输入电话号码" placeholder-class="placeholder" @input="handlePhoneInput" />
            </view>
          </view>

          <view class="form-item">
            <view class="item-label">服务地址</view>
            <view class="item-content" @click="handleSelectAddress">
              <text v-if="!parsedInfo.address" class="placeholder">点击选择服务地址</text>
              <text v-else class="parsed-info">{{ parsedInfo.address }}</text>
              <u-icon name="map" color="#fdd118" size="16"></u-icon>
            </view>
          </view>

          <!-- 地图预览区域 -->
          <view v-if="selectedLocation.latitude && selectedLocation.longitude" class="map-preview-container">
            <view class="map-preview-header">
              <text class="map-preview-title">位置预览</text>
              <view class="map-preview-actions">
                <view class="map-action-btn" @click="handleSelectAddress">
                  <u-icon name="edit" size="12" color="#007AFF"></u-icon>
                  <text class="action-text">重选</text>
                </view>
              </view>
            </view>
            <view class="map-preview-content" @click="showLocationOnMap">
              <map
                class="preview-map"
                :latitude="selectedLocation.latitude"
                :longitude="selectedLocation.longitude"
                :markers="mapMarkers"
                :scale="16"
                :enable-zoom="false"
                :enable-scroll="false"
                :enable-rotate="false"
                :show-location="false"
              ></map>
              <view class="map-overlay">
                <view class="location-info">
                  <text class="location-address">{{ selectedLocation.address || '位置信息' }}</text>
                  <text class="location-coords">{{ selectedLocation.latitude.toFixed(4) }}, {{ selectedLocation.longitude.toFixed(4) }}</text>
                </view>
                <view class="map-tip">
                  <u-icon name="eye" size="12" color="#fff"></u-icon>
                  <text class="tip-text">点击查看大图</text>
                </view>
              </view>
            </view>
          </view>

          <view class="form-item">
            <view class="item-label">门牌号</view>
            <view class="item-content">
              <input type="text" v-model="doorNumber" placeholder="请输入详细门牌号" placeholder-class="placeholder" />
            </view>
          </view>
        </view>
      </view>

      <!-- 订单信息卡片 -->
      <view class="info-card order-info-card">
        <view class="card-header">
          <view class="header-icon order-icon">
            <u-icon name="list" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">订单信息</text>
        </view>
        <view class="form-section">
          <view class="form-item">
            <view class="item-label required">项目</view>
            <view class="item-content" @click="handleSelectProject">
              <text v-if="!selectedSku" class="placeholder">点击查看项目价格表</text>
              <view v-else class="selected-product">
                <view class="product-info">
                  <text class="product-name">{{ selectedSku.skuName }}</text>
                </view>
              </view>
              <u-icon name="arrow-right" color="#ccc" size="14"></u-icon>
            </view>
          </view>

          <view class="form-item">
            <view class="item-label required">购买金额(元)</view>
            <view class="item-content">
              <input type="digit" v-model="amount" placeholder="请输入客户购买金额" placeholder-class="placeholder" />
            </view>
          </view>

          <!-- 支付方式选择 -->
          <view class="form-item">
            <view class="item-label required">支付方式</view>
            <view class="item-content">
              <view class="payment-methods">
                <view
                  class="payment-method active"
                >
                  <view class="method-icon cash-icon">
                    <u-icon name="rmb-circle-fill" size="16" color="#fff"></u-icon>
                  </view>
                  <text class="method-name">现金支付</text>
                </view>
              </view>
            </view>
          </view>

          <view class="form-item">
            <view class="item-label required">服务日期</view>
            <view class="item-content" @click="handleSelectDate">
              <text v-if="!serviceDate" class="placeholder">请选择服务日期</text>
              <text v-else class="selected-text">{{ serviceDate }}</text>
              <u-icon name="calendar" color="#fdd118" size="16"></u-icon>
            </view>
          </view>

          <view class="form-item">
            <view class="item-label required">服务时间</view>
            <view class="item-content" @click="handleSelectTime">
              <text v-if="!serviceTime" class="placeholder">请选择服务时间</text>
              <text v-else class="selected-text">{{ serviceTime }}</text>
              <u-icon name="clock" color="#fdd118" size="16"></u-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 服务提醒卡片 -->
      <view class="info-card service-notice-card">
        <view class="card-header">
          <view class="header-icon notice-icon">
            <u-icon name="chat" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">服务提醒</text>
          <text class="card-subtitle">（服务人员可见）</text>
        </view>
        <view class="card-content">
          <view class="notice-input">
            <textarea v-model="serviceNotice" placeholder="提醒服务人员服务注意事项，如需提前和客户联系" :maxlength="200"></textarea>
            <view class="counter">{{ serviceNotice.length }}/200</view>
          </view>
        </view>
      </view>

      <!-- 扩展信息卡片 -->
      <view class="info-card extend-info-card" v-if="hasExtendedFields">
        <view class="card-header">
          <view class="header-icon extend-icon">
            <u-icon name="plus" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">扩展信息</text>
        </view>
        <view class="form-section">
          <!-- 销售归属 -->
          <view class="form-item" v-if="selectedTags['销售归属']">
            <view class="item-label">销售归属</view>
            <view class="item-content" @click="handleSelectSalesAttribution">
              <text class="placeholder" v-if="!salesAttribution">请选择业绩归属销售</text>
              <text v-else>{{ salesAttribution }}</text>
              <u-icon name="arrow-right" color="#ccc" size="14"></u-icon>
            </view>
          </view>

          <!-- 销售提成 -->
          <view class="form-item" v-if="selectedTags['销售归属']">
            <view class="item-label">销售提成(元)</view>
            <view class="item-content">
              <input type="digit" v-model="salesCommission" placeholder="请输入销售提成" placeholder-class="placeholder" />
            </view>
          </view>

          <!-- 渠道编号 -->
          <view class="form-item" v-if="selectedTags['渠道编号']">
            <view class="item-label">渠道编号</view>
            <view class="item-content">
              <input type="text" v-model="channelCode" placeholder="请输入渠道编号" placeholder-class="placeholder" />
            </view>
          </view>

          <!-- 订单来源 -->
          <view class="form-item" v-if="selectedTags['订单来源']">
            <view class="item-label">订单来源</view>
            <view class="item-content" @click="handleSelectOrderSource">
              <text class="placeholder" v-if="!orderSource">请选择订单来源</text>
              <text v-else>{{ orderSource }}</text>
              <u-icon name="arrow-right" color="#ccc" size="14"></u-icon>
            </view>
          </view>

          <!-- 面积 -->
          <view class="form-item" v-if="selectedTags['面积']">
            <view class="item-label">面积</view>
            <view class="item-content">
              <input type="digit" v-model="area" placeholder="请输入面积" placeholder-class="placeholder" />
            </view>
          </view>

          <!-- 户型 -->
          <view class="form-item" v-if="selectedTags['户型']">
            <view class="item-label">户型</view>
            <view class="item-content">
              <input type="text" v-model="houseType" placeholder="请输入户型" placeholder-class="placeholder" />
            </view>
          </view>
        </view>
      </view>

      <!-- 备注信息卡片 -->
      <view class="info-card remarks-card" v-if="hasRemarkFields">
        <view class="card-header">
          <view class="header-icon remark-icon">
            <u-icon name="chat" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">备注信息</text>
        </view>
        <view class="card-content">
          <!-- 售后备注 -->
          <view class="remark-section" v-if="selectedTags['售后备注']">
            <view class="remark-title">售后备注（仅商家可见）</view>
            <view class="remark-input">
              <textarea v-model="afterSalesNote" placeholder="请输入售后备注" :maxlength="200"></textarea>
              <view class="counter">{{ afterSalesNote.length }}/200</view>
            </view>
          </view>

          <!-- 客户备注 -->
          <view class="remark-section" v-if="selectedTags['客户备注']">
            <view class="remark-title">客户备注</view>
            <view class="remark-input">
              <textarea v-model="customerNote" placeholder="请输入客户备注" :maxlength="200"></textarea>
              <view class="counter">{{ customerNote.length }}/200</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 字段选择卡片 -->
      <view class="info-card tags-card">
        <view class="card-header">
          <view class="header-icon tags-icon">
            <u-icon name="grid" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">添加更多字段</text>
        </view>
        <view class="card-content">
          <view class="tags-section">
            <!-- 第一行：3个标签 -->
            <view class="tags-row">
              <view class="tag-item" :class="{active: selectedTags['客户备注']}" @click="addTag('客户备注')">
                <view class="tag-icon">
                  <u-icon :name="selectedTags['客户备注'] ? 'minus-circle-fill' : 'plus-circle'" size="16" color="currentColor"></u-icon>
                </view>
                <text class="tag-text">客户备注</text>
              </view>
              <view class="tag-item" :class="{active: selectedTags['销售归属']}" @click="addTag('销售归属')">
                <view class="tag-icon">
                  <u-icon :name="selectedTags['销售归属'] ? 'minus-circle-fill' : 'plus-circle'" size="16" color="currentColor"></u-icon>
                </view>
                <text class="tag-text">销售归属</text>
              </view>
              <!-- <view class="tag-item" :class="{active: selectedTags['销售提成']}" @click="addTag('销售提成')">
                <view class="tag-icon">
                  <u-icon :name="selectedTags['销售提成'] ? 'minus-circle-fill' : 'plus-circle'" size="16" color="currentColor"></u-icon>
                </view>
                <text class="tag-text">销售提成</text>
              </view> -->
            </view>

            <!-- 第二行：3个标签 -->
            <!-- <view class="tags-row">
              <view class="tag-item" :class="{active: selectedTags['渠道编号']}" @click="addTag('渠道编号')">
                <view class="tag-icon">
                  <u-icon :name="selectedTags['渠道编号'] ? 'minus-circle-fill' : 'plus-circle'" size="16" color="currentColor"></u-icon>
                </view>
                <text class="tag-text">渠道编号</text>
              </view>
              <view class="tag-item" :class="{active: selectedTags['订单来源']}" @click="addTag('订单来源')">
                <view class="tag-icon">
                  <u-icon :name="selectedTags['订单来源'] ? 'minus-circle-fill' : 'plus-circle'" size="16" color="currentColor"></u-icon>
                </view>
                <text class="tag-text">订单来源</text>
              </view>
              <view class="tag-item" :class="{active: selectedTags['售后备注']}" @click="addTag('售后备注')">
                <view class="tag-icon">
                  <u-icon :name="selectedTags['售后备注'] ? 'minus-circle-fill' : 'plus-circle'" size="16" color="currentColor"></u-icon>
                </view>
                <text class="tag-text">售后备注</text>
              </view>
            </view> -->

            <!-- 第三行：2个标签，居中显示 -->
            <!-- <view class="tags-row tags-row-center">
              <view class="tag-item" :class="{active: selectedTags['面积']}" @click="addTag('面积')">
                <view class="tag-icon">
                  <u-icon :name="selectedTags['面积'] ? 'minus-circle-fill' : 'plus-circle'" size="16" color="currentColor"></u-icon>
                </view>
                <text class="tag-text">面积</text>
              </view>
              <view class="tag-item" :class="{active: selectedTags['户型']}" @click="addTag('户型')">
                <view class="tag-icon">
                  <u-icon :name="selectedTags['户型'] ? 'minus-circle-fill' : 'plus-circle'" size="16" color="currentColor"></u-icon>
                </view>
                <text class="tag-text">户型</text>
              </view>
            </view> -->
          </view>
        </view>
      </view>

      <!-- 温馨提示卡片 -->
      <view class="info-card tips-card">
        <view class="card-header">
          <view class="header-icon tips-icon">
            <u-icon name="info-circle" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">温馨提示</text>
        </view>
        <view class="card-content">
          <view class="tips-list">
            <!-- <view class="tips-item">
              <view class="tips-dot"></view>
              <text>可选电脑端，开启写码隐私保护功能</text>
            </view> -->
            <view class="tips-item">
              <view class="tips-dot"></view>
              <text>派单后会短信通知客户和服务人员</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作区域 -->
    <view class="bottom-actions">
      <view class="submit-btn" @click="handleSubmit" :class="{ 'disabled': submitting }">
        <u-icon v-if="!submitting" name="checkmark" size="18" color="#fff"></u-icon>
        <u-loading-icon v-else mode="spinner" color="#fff" size="18"></u-loading-icon>
        <text v-if="submitting">提交中...</text>
        <text v-else>{{ isStaffMode ? '确认预约' : '确认下单' }}</text>
      </view>
    </view>



    <!-- 日期选择器 -->
    <u-datetime-picker
      :show="showDatePicker"
      mode="date"
      :min-date="minDateTimestamp"
      @confirm="confirmDate"
      @cancel="showDatePicker = false"
    ></u-datetime-picker>

    <!-- 时间选择器 -->
    <view class="time-picker-modal" v-if="showTimePicker">
      <view class="picker-mask" @click="showTimePicker = false">
        <view class="picker-content" @click.stop>
          <view class="picker-header">
            <text class="picker-cancel" @click="showTimePicker = false">取消</text>
            <text class="picker-title">选择服务时间</text>
            <text class="picker-confirm" @click="confirmTimeSelection">确定</text>
          </view>
          <view class="time-options-container">
            <scroll-view scroll-y class="time-options-scroll">
              <view
                class="time-option"
                :class="{ 'selected': item === tempServiceTime }"
                v-for="item in timeOptions"
                :key="item"
                @click="selectTime(item)"
              >
                <text>{{ item }}</text>
                <u-icon v-if="item === tempServiceTime" name="checkmark" color="#fdd118" size="16"></u-icon>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </view>

    <!-- 员工选择组件 -->
    <staff-selector
      :show.sync="showStaffSelector"
      mode="single"
      title="选择销售归属"
      subtitle="请选择业绩归属销售人员"
      confirmText="确定"
      :showSearch="true"
      searchPlaceholder="搜索员工姓名或手机号"
      staffType="all"
      @confirm="handleStaffSelected"
      @cancel="handleStaffSelectorCancel"
    />
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { uploadFileToPublic } from '@/utlis/common.js'
import { voiceRecognizeByUrl, geocodeAddress } from '@/api/common.js'
import { get } from '@/utlis/require.js'
import StaffSelector from './components/staff-selector/staff-selector.vue'

export default {
  components: {
    StaffSelector
  },
  computed: {
    ...mapState(['StatusBar']),

    // 是否有扩展字段被选中
    hasExtendedFields() {
      return this.selectedTags['销售归属'] ||
             this.selectedTags['销售提成'] ||
             this.selectedTags['渠道编号'] ||
             this.selectedTags['订单来源'] ||
             this.selectedTags['面积'] ||
             this.selectedTags['户型']
    },

    // 是否有备注字段被选中
    hasRemarkFields() {
      return this.selectedTags['售后备注'] || this.selectedTags['客户备注']
    },

    // 计算销售提成
    calculatedSalesCommission() {
      // 如果没有选择销售归属或没有选中SKU，返回0
      if (!this.selectedTags['销售归属'] || !this.selectedSku || !this.amount) {
        return 0;
      }

      const purchaseAmount = parseFloat(this.amount) || 0;
      const commissionValue = parseFloat(this.selectedSku.skuCommission) || 0;
      const commissionType = this.selectedSku.skuCommissionType || 0;

      if (commissionType === 0) {
        // 固定金额
        return commissionValue;
      } else if (commissionType === 1) {
        // 百分比，计算百分比提成
        return (purchaseAmount * commissionValue / 100);
      }

      return 0;
    },



    // 格式化提成显示
    formattedSalesCommission() {
      const commission = this.calculatedSalesCommission;
      return commission > 0 ? commission.toFixed(2) : '0.00';
    }
  },

  data() {
    return {
      // 端类型识别
      isStaffMode: false, // 是否为员工端模式
      customerInfo: '', // 客户信息输入
      inputLength: 0, // 输入长度计数
      amount: '', // 购买金额
      serviceNotice: '', // 服务提醒
      doorNumber: '', // 门牌号
      // 支付方式相关
      paymentType: 'cash', // 支付方式：cash-现金支付
      // 语音识别相关（百度语音识别）
      recorderManager: null, // 原生录音管理器
      isRecording: false, // 是否正在录音
      recordingTimer: null, // 录音计时器
      recordingDuration: 0, // 录音时长（秒）
      maxRecordingDuration: 30, // 最大录音时长（秒）
      minRecordingDuration: 2, // 最小录音时长（秒）
      recordingFilePath: '', // 录音文件路径
      // 解析后的客户信息
      parsedInfo: {
        name: '', // 客户姓名
        phone: '', // 客户电话
        address: '', // 客户地址
      },
      // 地图选点信息
      selectedLocation: {
        latitude: 0,
        longitude: 0,
        address: '',
        name: ''
      },
      // 地图标记
      mapMarkers: [],
      // 标签选中状态
      selectedTags: {
        '客户备注': false,
        '销售归属': false,
        '销售提成': false,
        '渠道编号': false,
        '订单来源': false,
        '售后备注': false,
        '面积': false,
        '户型': false
      },
      // 额外字段
      customerNote: '', // 客户备注
      salesAttribution: '', // 销售归属（显示名称）
      selectedSalesStaff: null, // 选中的销售员工对象
      salesCommission: '', // 销售提成
      channelCode: '', // 渠道编号
      orderSource: '', // 订单来源
      afterSalesNote: '', // 售后备注
      area: '', // 面积
      houseType: '', // 户型
      // 员工选择相关
      showStaffSelector: false, // 显示员工选择器
      // 选中的SKU信息
      selectedSku: null,
      // 服务日期时间
      serviceDate: '', // 服务日期
      serviceTime: '', // 服务时间
      tempServiceTime: '', // 临时服务时间（用于picker）
      showDatePicker: false, // 显示日期选择器
      showTimePicker: false, // 显示时间选择器
      minDateTimestamp: new Date().getTime(), // 最小日期时间戳
      // 时间选项（00:00-23:00，每小时一个选项）
      timeOptions: [
        '00:00', '01:00', '02:00', '03:00', '04:00', '05:00',
        '06:00', '07:00', '08:00', '09:00', '10:00', '11:00',
        '12:00', '13:00', '14:00', '15:00', '16:00', '17:00',
        '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'
      ],
      // 提交状态
      submitting: false,
      // 移除了外部客户检查相关变量
      // 门店信息
      storeInfo: null, // 门店信息
      storeBusinessHours: '08:00-18:00', // 门店营业时间，默认值
    };
  },

  onReady() {
    this.initRecorder();
  },

  onLoad(options) {
    console.log('sub-po页面接收到的参数:', options);

    // 识别是否为员工端模式
    this.isStaffMode = options.from === 'staff';
    console.log('当前模式:', this.isStaffMode ? '员工端' : '门店端');

    // 如果传入了clientId，获取客户信息并自动填入
    if (options.clientId) {
      this.loadClientInfo(options.clientId);
    }
  },

  onShow() {
    // 监听SKU选择事件（备用机制）
    uni.$on('skuSelected', this.handleSkuSelected);

    // 检查存储中的SKU数据（主要机制）
    this.checkStoredSku();

    // 获取门店信息，用于设置服务时间选项
    this.loadStoreInfo();
  },

  onHide() {
    // 移除事件监听
    uni.$off('skuSelected', this.handleSkuSelected);
  },

  onUnload() {
    // 页面卸载时清理资源
    this.clearRecordingTimer();
    if (this.isRecording && this.recorderManager) {
      try {
        this.recorderManager.stop();
      } catch (error) {
        console.error('页面卸载时停止录音失败:', error);
      }
    }
  },

  methods: {
    // 加载客户信息并自动填入手机号
    async loadClientInfo(clientId) {
      try {
        console.log('开始获取客户信息, clientId:', clientId);
        
        // 先尝试从客户列表API获取信息，避免调用不存在的详情接口
        const clientInfo = await this.getClientFromList(clientId);
        
        if (clientInfo) {
          // 自动填入手机号
          this.parsedInfo.phone = clientInfo.mobile || '';
          
          // 如果存在姓名，也自动填入
          if (clientInfo.name) {
            this.parsedInfo.name = clientInfo.name;
          }

          // 根据填入的信息显示提示
          let message = '客户手机号已自动填入';
          if (this.parsedInfo.name) {
            message = '客户信息已自动填入';
          }
          
          uni.showToast({
            title: message,
            icon: 'success',
            duration: 1500
          });
          
          console.log('客户信息填入成功:', {
            name: this.parsedInfo.name,
            phone: this.parsedInfo.phone
          });
        } else {
          throw new Error('未找到客户信息');
        }
        
      } catch (error) {
        console.error('加载客户信息失败:', error);
        
        // 显示失败提示，但不阻断操作
        uni.showToast({
          title: '客户信息加载失败，请手动填入',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 从客户列表中获取客户信息
    async getClientFromList(clientId) {
      try {
        // 调用客户列表API并查找指定ID的客户
        const { getClientList } = await import('@/api/client.js');
        const result = await getClientList({ page: 1, size: 100 }); // 获取客户列表
        
        console.log('客户列表API响应:', result);
        
        // 从返回结果中查找指定ID的客户
        let clientList = [];
        if (Array.isArray(result)) {
          clientList = result;
        } else if (result.list && Array.isArray(result.list)) {
          clientList = result.list;
        } else if (result.data && Array.isArray(result.data)) {
          clientList = result.data;
        }
        
        // 查找指定ID的客户
        const client = clientList.find(item => item.id == clientId);
        
        console.log('找到的客户信息:', client);
        return client;
        
      } catch (error) {
        console.error('获取客户列表失败:', error);
        throw error;
      }
    },


    // 初始化录音管理器（百度语音识别）
    initRecorder() {
      try {
        this.recorderManager = uni.getRecorderManager();

        if (!this.recorderManager) {
          console.error('录音管理器初始化失败');
          uni.showToast({
            title: '录音功能不可用',
            icon: 'none'
          });
          return;
        }

        // 监听录音开始
        this.recorderManager.onStart(() => {
          console.log('录音开始');
          this.isRecording = true;
          uni.showToast({
            title: '正在录音，请清晰说话...',
            icon: 'none',
            duration: 2000
          });
        });

        // 监听录音结束
        this.recorderManager.onStop((res) => {
          console.log('录音结束:', res);
          console.log('录音时长:', this.recordingDuration, '秒');
          console.log('录音文件路径:', res.tempFilePath);
          console.log('录音文件大小:', res.fileSize);

          this.isRecording = false;
          this.clearRecordingTimer();
          this.recordingFilePath = res.tempFilePath;

          // 检查录音时长
          if (this.recordingDuration < this.minRecordingDuration) {
            uni.showToast({
              title: `录音时间太短，请至少录音${this.minRecordingDuration}秒`,
              icon: 'none',
              duration: 2000
            });
            return;
          }

          // 上传录音文件进行识别
          this.uploadAndRecognize(res.tempFilePath);
        });

        // 监听录音错误
        this.recorderManager.onError((err) => {
          console.error('录音错误:', err);
          this.isRecording = false;
          this.clearRecordingTimer();

          uni.showToast({
            title: '录音失败，请重试',
            icon: 'none',
            duration: 2000
          });
        });

        console.log('录音管理器初始化成功');
      } catch (error) {
        console.error('初始化录音管理器失败:', error);
        uni.showToast({
          title: '录音功能初始化失败',
          icon: 'none'
        });
      }
    },

    // 切换语音输入状态
    toggleVoiceInput() {
      if (!this.recorderManager) {
        console.error('录音管理器不可用');
        uni.showToast({
          title: '录音功能不可用，请重新进入页面',
          icon: 'none'
        });
        return;
      }

      if (this.isRecording) {
        // 当前正在录音，停止录音
        this.stopVoiceRecording();
      } else {
        // 当前未录音，检查权限后开始录音
        this.checkRecordPermissionAndStart();
      }
    },

    // 检查录音权限并开始录音
    checkRecordPermissionAndStart() {
      // #ifdef MP-WEIXIN
      uni.getSetting({
        success: (res) => {
          console.log('权限设置:', res.authSetting);

          if (res.authSetting['scope.record'] === false) {
            // 用户拒绝了录音权限
            uni.showModal({
              title: '需要录音权限',
              content: '语音识别需要录音权限，请在设置中开启',
              showCancel: true,
              cancelText: '取消',
              confirmText: '去设置',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  uni.openSetting({
                    success: (settingRes) => {
                      console.log('设置页面返回:', settingRes.authSetting);
                      if (settingRes.authSetting['scope.record']) {
                        this.startVoiceRecording();
                      }
                    }
                  });
                }
              }
            });
          } else {
            // 权限正常，开始录音
            this.startVoiceRecording();
          }
        },
        fail: (error) => {
          console.error('获取权限设置失败:', error);
          // 权限检查失败，直接尝试录音
          this.startVoiceRecording();
        }
      });
      // #endif

      // #ifndef MP-WEIXIN
      // 非微信小程序环境，直接开始录音
      this.startVoiceRecording();
      // #endif
    },

    // 开始语音录音
    startVoiceRecording() {
      try {
        console.log('开始录音');
        this.recordingDuration = 0;

        // 启动录音，配置录音参数
        const startOptions = {
          duration: this.maxRecordingDuration * 1000, // 最大录音时长（毫秒）
          sampleRate: 16000, // 采样率
          numberOfChannels: 1, // 录音通道数
          encodeBitRate: 96000, // 编码码率
          format: 'wav' // 录音格式（使用百度直接支持的格式）
        };

        console.log('录音参数:', startOptions);
        this.recorderManager.start(startOptions);

        // 启动计时器
        this.startRecordingTimer();

      } catch (error) {
        console.error('启动录音失败:', error);
        this.isRecording = false;
        uni.showToast({
          title: '启动录音失败，请重试',
          icon: 'none'
        });
      }
    },

    // 停止语音录音
    stopVoiceRecording() {
      // 检查录音时长是否足够
      if (this.recordingDuration < this.minRecordingDuration) {
        uni.showToast({
          title: `请至少录音${this.minRecordingDuration}秒`,
          icon: 'none',
          duration: 2000
        });
        return;
      }

      try {
        console.log(`停止录音，录音时长: ${this.recordingDuration}秒`);
        this.recorderManager.stop();
        this.clearRecordingTimer();
      } catch (error) {
        console.error('停止录音失败:', error);
        this.isRecording = false;
        this.clearRecordingTimer();
        uni.showToast({
          title: '停止录音失败',
          icon: 'none'
        });
      }
    },

    // 启动录音计时器
    startRecordingTimer() {
      this.clearRecordingTimer();
      this.recordingTimer = setInterval(() => {
        this.recordingDuration++;

        // 检查是否达到最大录音时长
        if (this.recordingDuration >= this.maxRecordingDuration) {
          console.log('录音时长达到上限，自动停止');
          this.stopVoiceRecording();
          uni.showToast({
            title: `录音时长已达${this.maxRecordingDuration}秒上限`,
            icon: 'none',
            duration: 2000
          });
        }
      }, 1000);
    },

    // 清除录音计时器
    clearRecordingTimer() {
      if (this.recordingTimer) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = null;
      }
    },

    // 处理手机号输入
    handlePhoneInput(e) {
      // 移除了外部用户校验逻辑，只保留基本输入处理
      const phone = e.detail.value;
      // 手机号输入处理逻辑可以在这里添加
    },

    // 验证手机号格式
    validatePhoneNumber(phone) {
      // 中国大陆手机号正则：1开头，第二位是3-9，总共11位数字
      const phoneRegex = /^1[3-9]\d{9}$/;
      return phoneRegex.test(phone);
    },

    // 移除了外部客户检查相关方法

    // 上传录音文件并进行语音识别
    async uploadAndRecognize(filePath) {
      try {
        uni.showLoading({
          title: '正在识别语音...',
          mask: true
        });

        console.log('开始上传录音文件:', filePath);

        // 上传录音文件到后端进行识别
        const uploadResult = await this.uploadRecordingFile(filePath);

        if (uploadResult && uploadResult.text) {
          // 识别成功，将结果添加到输入框
          this.customerInfo += uploadResult.text;
          this.inputLength = this.customerInfo.length;

          uni.hideLoading();
          uni.showToast({
            title: `语音识别成功 (${this.recordingDuration}秒)`,
            icon: 'success',
            duration: 1500
          });
        } else {
          throw new Error('识别结果为空');
        }

      } catch (error) {
        console.error('语音识别失败:', error);
        uni.hideLoading();

        let errorMsg = '语音识别失败';
        if (error.message) {
          errorMsg = error.message;
        }

        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });
      }
    },

    // 上传录音文件并进行语音识别
    async uploadRecordingFile(filePath) {
      try {
        // 第一步：使用公共文件上传方法（无需token验证）
        const uploadResult = await uploadFileToPublic(filePath, {
          showLoading: false, // 我们在外层已经显示了加载提示
          showToast: false    // 我们在外层处理提示
        });

        // 第二步：调用语音识别接口
        const recognizeResult = await this.callVoiceRecognizeAPI(uploadResult.data);

        return recognizeResult;
      } catch (error) {
        console.error('语音识别流程失败:', error);
        throw error;
      }
    },

    // 调用语音识别API
    async callVoiceRecognizeAPI(fileInfo) {
      try {
        // 使用导入的API方法调用语音识别接口
        // 注意：公共接口返回的字段名为 file_url 和 file_name
        const recognizeData = {
          fileUrl: fileInfo.file_url || fileInfo.url,
          fileName: fileInfo.file_name || fileInfo.fileName,
          duration: this.recordingDuration
        };

        const result = await voiceRecognizeByUrl(recognizeData);

        // require.js 已经处理了成功响应，直接使用返回的数据
        // 如果执行到这里说明请求成功，result 就是业务数据
        if (result && result.text) {
          return result;
        } else {
          throw new Error('语音识别结果为空');
        }
      } catch (error) {
        console.error('语音识别API调用失败:', error);
        // 如果是网络错误或其他异常，抛出更具体的错误信息
        if (error.message) {
          throw new Error(error.message);
        } else {
          throw new Error('语音识别服务异常');
        }
      }
    },

    // 返回上一页
    handleBack() {
      uni.navigateBack();
    },

    // 输入框计数
    handleInput(e) {
      this.inputLength = e.detail.value.length;
    },

    // 识别客户信息
    handleIdentify() {
      if (!this.customerInfo.trim()) {
        uni.showToast({
          title: '请先输入客户信息',
          icon: 'none',
        });
        return;
      }

      uni.showToast({
        title: '正在识别...',
        icon: 'loading',
        duration: 1000
      });

      // 延迟执行识别逻辑，让用户看到加载提示
      setTimeout(() => {
        this.performIdentification();
      }, 500);
    },

    // 执行识别逻辑
    performIdentification() {
      const text = this.customerInfo.trim();
      let identifiedInfo = {
        name: '',
        phone: '',
        address: '',
        doorNumber: ''
      };

      // 方法1：尝试正则表达式识别
      const regexResult = this.identifyByRegex(text);
      if (regexResult.name || regexResult.phone || regexResult.address) {
        identifiedInfo = { ...identifiedInfo, ...regexResult };
      }

      // 方法2：尝试结构化文本识别
      const structuredResult = this.identifyByStructure(text);
      if (structuredResult.name || structuredResult.phone || structuredResult.address) {
        // 结构化识别的结果优先级更高
        identifiedInfo = { ...identifiedInfo, ...structuredResult };
      }

      // 更新解析结果
      this.parsedInfo = identifiedInfo;

      // 显示识别结果
      this.showIdentificationResult(identifiedInfo);
    },

    // 正则表达式识别
    identifyByRegex(text) {
      const result = {
        name: '',
        phone: '',
        address: '',
        doorNumber: ''
      };

      // 识别手机号（11位数字，1开头）
      const phoneRegex = /1[3-9]\d{9}/g;
      const phoneMatches = text.match(phoneRegex);
      if (phoneMatches && phoneMatches.length > 0) {
        result.phone = phoneMatches[0];
      }

      // 识别中文姓名（2-4个中文字符，常见姓氏开头）
      const nameRegex = /[\u4e00-\u9fa5]{2,4}(?=[\s,，。、]|$)/g;
      const nameMatches = text.match(nameRegex);
      if (nameMatches && nameMatches.length > 0) {
        // 过滤掉可能是地址的部分
        const filteredNames = nameMatches.filter(name =>
          !name.includes('省') && !name.includes('市') && !name.includes('区') &&
          !name.includes('县') && !name.includes('镇') && !name.includes('街道') &&
          !name.includes('小区') && !name.includes('路') && !name.includes('号')
        );
        if (filteredNames.length > 0) {
          result.name = filteredNames[0];
        }
      }

      // 识别地址（包含省市区等关键词的文本）
      const addressRegex = /[\u4e00-\u9fa5]*[省市区县镇街道村路号巷弄里][\u4e00-\u9fa5\d\-\s]*[号室栋楼层]?[\u4e00-\u9fa5\d\-\s]*/g;
      const addressMatches = text.match(addressRegex);
      if (addressMatches && addressMatches.length > 0) {
        // 选择最长的地址匹配
        const fullAddress = addressMatches.reduce((longest, current) =>
          current.length > longest.length ? current : longest, ''
        );
        result.address = fullAddress;

        // 尝试从地址中提取门牌号
        const doorNumberRegex = /(\d+[-\d]*号?[室栋楼层]?[\d\-]*)/;
        const doorMatch = fullAddress.match(doorNumberRegex);
        if (doorMatch) {
          result.doorNumber = doorMatch[1];
        }
      }

      return result;
    },

    // 结构化文本识别
    identifyByStructure(text) {
      const result = {
        name: '',
        phone: '',
        address: '',
        doorNumber: ''
      };

      // 字段映射
      const fieldMapping = {
        '姓名': 'name',
        '客户': 'name',
        '联系人': 'name',
        '电话': 'phone',
        '手机': 'phone',
        '联系方式': 'phone',
        '地址': 'address',
        '住址': 'address',
        '具体地址': 'address',
        '服务地址': 'address',
        '门牌号': 'doorNumber',
        '详细地址': 'doorNumber',
        '房号': 'doorNumber'
      };

      // 按行分割文本
      const lines = text.split(/[\n\r]+/);

      for (const line of lines) {
        // 尝试匹配 "字段：值" 或 "字段:值" 格式
        const colonMatch = line.match(/^(.+?)[：:]\s*(.+)$/);
        if (colonMatch) {
          const [, key, value] = colonMatch;
          const trimmedKey = key.trim();
          const trimmedValue = value.trim();

          if (fieldMapping[trimmedKey] && trimmedValue) {
            result[fieldMapping[trimmedKey]] = trimmedValue;
          }
        }
      }

      return result;
    },

    // 显示识别结果
    showIdentificationResult(identifiedInfo) {
      const { name, phone, address, doorNumber } = identifiedInfo;

      if (!name && !phone && !address && !doorNumber) {
        uni.showToast({
          title: '未能识别出有效信息，请检查输入格式',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 直接应用识别结果，不需要弹窗确认
      this.applyIdentificationResult(identifiedInfo);
    },

    // 应用识别结果到表单
    applyIdentificationResult(identifiedInfo) {
      const { name, phone, address, doorNumber } = identifiedInfo;

      // 应用门牌号到表单
      if (doorNumber) {
        this.doorNumber = doorNumber;
      }

      let successMessage = '识别成功！';
      if (name) successMessage += ` 姓名：${name}`;
      if (phone) successMessage += ` 电话：${phone}`;
      if (address) {
        successMessage += ` 地址已解析`;
        // 如果识别到地址，尝试获取经纬度
        this.geocodeAddress(address);
      }
      if (doorNumber) successMessage += ` 门牌号：${doorNumber}`;

      uni.showToast({
        title: successMessage,
        icon: 'success',
        duration: 2000
      });
    },

    // 地址转经纬度（地理编码）
    async geocodeAddress(address) {
      if (!address || !address.trim()) {
        return;
      }

      try {
        // 显示地理编码进度
        uni.showLoading({
          title: '正在获取位置信息...',
          mask: true
        });

        const result = await this.callGeocodeAPI(address.trim());

        if (result && result.location) {
          // 更新位置信息
          this.selectedLocation = {
            latitude: result.location.lat,
            longitude: result.location.lng,
            address: result.formatted_addresses?.recommend || address,
            name: result.formatted_addresses?.recommend || address
          };

          // 更新地图标记
          this.updateMapMarkers();

          uni.hideLoading();

          // 显示成功提示
          uni.showToast({
            title: '位置信息获取成功',
            icon: 'success',
            duration: 1500
          });

          console.log('地理编码成功:', this.selectedLocation);
        } else {
          throw new Error('地理编码返回数据格式错误');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('地理编码失败:', error);

        // 显示失败提示，但不阻断用户操作
        uni.showToast({
          title: '自动获取位置失败，可手动选择',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 调用后端地理编码API
    async callGeocodeAPI(address) {
      try {
        console.log('调用后端地理编码API:', address);

        // 调用后端地理编码接口
        const result = await geocodeAddress(address);

        console.log('后端地理编码API响应:', result);

        // 检查响应数据结构
        if (result && result.result && result.result.location) {
          // 返回与原来兼容的数据格式
          return {
            location: result.result.location,
            formatted_addresses: result.result.formatted_addresses,
            address_components: result.result.address_components
          };
        } else {
          throw new Error('地理编码返回数据格式错误');
        }
      } catch (error) {
        console.error('后端地理编码API调用失败:', error);

        // 处理错误信息
        let errorMessage = '地理编码失败';
        if (error.message) {
          errorMessage = error.message;
        } else if (error.msg) {
          errorMessage = error.msg;
        }

        throw new Error(errorMessage);
      }
    },



    // 选择服务地址
    handleSelectAddress() {
      // 如果已经有识别的地址，可以作为默认位置
      const defaultLocation = {};
      if (this.selectedLocation.latitude && this.selectedLocation.longitude) {
        defaultLocation.latitude = this.selectedLocation.latitude;
        defaultLocation.longitude = this.selectedLocation.longitude;
      }

      uni.chooseLocation({
        ...defaultLocation,
        success: (res) => {
          console.log('选择的位置信息：', res);

          // 更新选中的位置信息
          this.selectedLocation = {
            latitude: res.latitude,
            longitude: res.longitude,
            address: res.address,
            name: res.name || res.address
          };

          // 更新解析信息中的地址
          this.parsedInfo.address = res.address;

          // 更新地图标记
          this.updateMapMarkers();

          // 显示详细的位置信息
          const locationInfo = `地址: ${res.address}\n经度: ${res.longitude.toFixed(6)}\n纬度: ${res.latitude.toFixed(6)}`;
          console.log('详细位置信息:', locationInfo);

          uni.showToast({
            title: '地址选择成功',
            icon: 'success',
            duration: 1500
          });
        },
        fail: (err) => {
          console.error('选择位置失败：', err);
          uni.showToast({
            title: '选择位置失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },

    // 选择项目
    handleSelectProject() {
      // 根据端类型构建跳转URL
      const baseUrl = '/pages-home/product-list';
      const url = this.isStaffMode ? `${baseUrl}?from=staff` : baseUrl;

      // 跳转到产品列表页面
      uni.navigateTo({
        url: url,
        success: () => {
          console.log(`${this.isStaffMode ? '员工端' : '门店端'}跳转到产品列表页面成功`);
        },
        fail: (error) => {
          console.error('跳转到产品列表页面失败:', error);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },

    // 选择服务日期
    handleSelectDate() {
      this.showDatePicker = true;
    },

    // 选择服务时间
    handleSelectTime() {
      this.tempServiceTime = this.serviceTime || '09:00'; // 设置默认时间
      this.showTimePicker = true;
    },

    // 选择时间选项
    selectTime(time) {
      this.tempServiceTime = time;
    },

    // 确认时间选择
    confirmTimeSelection() {
      if (this.tempServiceTime) {
        this.serviceTime = this.tempServiceTime;
        this.showTimePicker = false;

        uni.showToast({
          title: `已选择时间: ${this.serviceTime}`,
          icon: 'success',
          duration: 1500
        });
      } else {
        uni.showToast({
          title: '请选择时间',
          icon: 'none',
          duration: 1500
        });
      }
    },

    // 确认日期选择
    confirmDate(e) {
      console.log('日期选择器返回的数据:', e);

      try {
        let dateString = '';

        // 处理不同格式的日期数据
        if (e.value) {
          // 如果是时间戳
          if (typeof e.value === 'number') {
            const date = new Date(e.value);
            dateString = this.formatDate(date);
          }
          // 如果是字符串格式的日期
          else if (typeof e.value === 'string') {
            const date = new Date(e.value);
            if (!isNaN(date.getTime())) {
              dateString = this.formatDate(date);
            } else {
              // 如果是 "YYYY-MM-DD" 格式的字符串
              dateString = e.value;
            }
          }
          // 如果是Date对象
          else if (e.value instanceof Date) {
            dateString = this.formatDate(e.value);
          }
        }

        // 验证日期格式
        if (dateString && dateString !== 'NaN-NaN-NaN') {
          this.serviceDate = dateString;
          this.showDatePicker = false;

          uni.showToast({
            title: `已选择日期: ${this.serviceDate}`,
            icon: 'success',
            duration: 1500
          });
        } else {
          throw new Error('日期格式无效');
        }

      } catch (error) {
        console.error('日期选择处理失败:', error);
        uni.showToast({
          title: '日期选择失败，请重试',
          icon: 'none',
          duration: 2000
        });
        this.showDatePicker = false;
      }
    },

    // 确认时间选择
    confirmTime(e) {
      console.log('时间选择器返回的数据:', e);

      try {
        let timeString = '';

        // 处理不同格式的时间数据
        if (e.value) {
          // 如果是时间戳
          if (typeof e.value === 'number') {
            const time = new Date(e.value);
            timeString = this.formatTime(time);
          }
          // 如果是字符串格式的时间
          else if (typeof e.value === 'string') {
            // 尝试解析字符串时间
            const time = new Date(e.value);
            if (!isNaN(time.getTime())) {
              timeString = this.formatTime(time);
            } else {
              // 如果是 "HH:MM" 格式的字符串
              timeString = e.value;
            }
          }
          // 如果是Date对象
          else if (e.value instanceof Date) {
            timeString = this.formatTime(e.value);
          }
        }

        // 验证时间格式
        if (timeString && timeString !== 'NaN:NaN') {
          this.serviceTime = timeString;
          this.showTimePicker = false;

          uni.showToast({
            title: `已选择时间: ${this.serviceTime}`,
            icon: 'success',
            duration: 1500
          });
        } else {
          throw new Error('时间格式无效');
        }

      } catch (error) {
        console.error('时间选择处理失败:', error);
        uni.showToast({
          title: '时间选择失败，请重试',
          icon: 'none',
          duration: 2000
        });
        this.showTimePicker = false;
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date || isNaN(date.getTime())) {
        return '';
      }
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 格式化时间
    formatTime(date) {
      if (!date || isNaN(date.getTime())) {
        return '';
      }
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${hours}:${minutes}`;
    },

    // 检查存储中的SKU数据
    checkStoredSku() {
      try {
        const storedSku = uni.getStorageSync('selectedSku');
        if (storedSku) {
          console.log('从存储中获取到选中的SKU:', storedSku);
          this.selectedSku = storedSku;

          // 自动填充购买金额
          this.amount = storedSku.skuPrice.toString();

          // 如果已经选择了销售归属，自动填充提成
          if (this.selectedTags['销售归属']) {
            this.updateSalesCommission();
          }

          // 清理存储数据
          uni.removeStorageSync('selectedSku');

          // 显示选择成功的提示
          uni.showToast({
            title: `已选择: ${storedSku.skuName}`,
            icon: 'success',
            duration: 2000
          });
        }
      } catch (error) {
        console.error('检查存储SKU数据失败:', error);
      }
    },

    // 处理SKU选择（备用机制）
    handleSkuSelected(sku) {
      console.log('通过事件接收到选中的SKU:', sku);

      // 如果当前没有选中SKU，则使用事件传递的SKU
      if (!this.selectedSku) {
        this.selectedSku = sku;

        // 自动填充购买金额
        this.amount = sku.skuPrice.toString();

        // 如果已经选择了销售归属，自动填充提成
        if (this.selectedTags['销售归属']) {
          this.updateSalesCommission();
        }

        // 显示选择成功的提示
        uni.showToast({
          title: `已选择: ${sku.skuName}`,
          icon: 'success',
          duration: 2000
        });
      }
    },

    // 截断文本
    truncateText(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    },

    // 选择销售归属
    handleSelectSalesAttribution() {
      console.log('打开员工选择器');
      this.showStaffSelector = true;
    },

    // 员工选择确认回调
    handleStaffSelected(staff) {
      console.log('选中员工:', staff);
      if (staff) {
        this.selectedSalesStaff = staff;
        this.salesAttribution = staff.real_name || staff.name || '未知';

        // 自动填充销售提成
        this.updateSalesCommission();

        uni.showToast({
          title: `已选择：${this.salesAttribution}`,
          icon: 'success',
          duration: 1500
        });
      }
      this.showStaffSelector = false;
    },

    // 员工选择取消回调
    handleStaffSelectorCancel() {
      console.log('取消员工选择');
      this.showStaffSelector = false;
    },

    // 更新销售提成
    updateSalesCommission() {
      // 只有在有SKU信息和金额时才自动填充
      if (this.selectedSku && this.amount) {
        const calculatedCommission = this.calculatedSalesCommission;
        if (calculatedCommission > 0) {
          this.salesCommission = calculatedCommission.toFixed(2);
          console.log('自动填充销售提成:', this.salesCommission);
        }
      }
    },

    // 选择订单来源
    handleSelectOrderSource() {
      uni.showToast({
        title: '选择订单来源',
        icon: 'none',
      });
    },

    // 添加标签
    addTag(type) {
      // 切换标签选中状态
      this.selectedTags[type] = !this.selectedTags[type];

      // 如果是添加销售提成标签，自动填入计算好的金额
      if (type === '销售提成' && this.selectedTags[type]) {
        this.updateSalesCommission();
      }

      // 显示提示
      uni.showToast({
        title: this.selectedTags[type] ? `添加${type}` : `移除${type}`,
        icon: 'none',
      });
    },

    // 更新地图标记
    updateMapMarkers() {
      if (this.selectedLocation.latitude && this.selectedLocation.longitude) {
        this.mapMarkers = [{
          id: 1,
          latitude: this.selectedLocation.latitude,
          longitude: this.selectedLocation.longitude,
          iconPath: 'https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/img/locate.png',
          width: 24,
          height: 24,
          callout: {
            content: this.selectedLocation.address || '服务地址',
            color: '#333',
            fontSize: 12,
            borderRadius: 4,
            bgColor: '#fff',
            padding: 8,
            display: 'ALWAYS'
          }
        }];
      } else {
        this.mapMarkers = [];
      }
    },

    // 显示位置大地图
    showLocationOnMap() {
      if (!this.selectedLocation.latitude || !this.selectedLocation.longitude) {
        uni.showToast({
          title: '暂无位置信息',
          icon: 'none',
          duration: 1500
        });
        return;
      }

      // 使用uni.openLocation打开系统地图
      uni.openLocation({
        latitude: this.selectedLocation.latitude,
        longitude: this.selectedLocation.longitude,
        name: '服务地址',
        address: this.selectedLocation.address || '',
        scale: 16,
        success: () => {
          console.log('打开地图成功');
        },
        fail: (err) => {
          console.error('打开地图失败:', err);
          uni.showToast({
            title: '打开地图失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },

    // 显示位置详情（包含经纬度）
    showLocationDetails() {
      if (!this.selectedLocation.latitude || !this.selectedLocation.longitude) {
        uni.showToast({
          title: '暂无位置信息',
          icon: 'none',
          duration: 1500
        });
        return;
      }

      const locationDetails = `地址: ${this.selectedLocation.address || '未知地址'}\n经度: ${this.selectedLocation.longitude.toFixed(6)}\n纬度: ${this.selectedLocation.latitude.toFixed(6)}`;

      uni.showModal({
        title: '位置详情',
        content: locationDetails,
        showCancel: false,
        confirmText: '确定'
      });
    },

    // 测试地址反解析功能（开发测试用）
    async testGeocoding() {
      const testAddress = '福建省厦门市思明区滨海街道黄厝村黄厝社3号';
      console.log('测试地址反解析:', testAddress);
      await this.geocodeAddress(testAddress);
    },

    // 提交表单
    async handleSubmit() {
      // 防止重复提交
      if (this.submitting) {
        return;
      }

      // 检查必填项
      const validationResult = this.validateForm();
      if (!validationResult.isValid) {
        uni.showToast({
          title: validationResult.message,
          icon: 'none',
          duration: 2000
        });
        return;
      }

      try {
        this.submitting = true;

        // 显示加载提示
        uni.showLoading({
          title: this.isStaffMode ? '正在创建预约...' : '正在提交订单...',
          mask: true
        });

        // 准备提交数据，参考client端接口格式
        const submitData = this.prepareSubmitData();

        console.log('提交数据:', submitData);

        // 调用下单接口
        const result = await this.submitOrder(submitData);

        uni.hideLoading();

        // 显示成功提示
        uni.showToast({
          title: this.isStaffMode ? '预约创建成功' : '订单创建成功',
          icon: 'success',
          duration: 2000
        });

        // 延迟跳转到订单详情或返回上一页
        setTimeout(() => {
          if (result && result.order_number) {
            // 如果有订单号，跳转到订单详情（使用与订单列表一致的页面）
            uni.redirectTo({
              url: `/pages-dispatch/order-detail-new?orderNumber=${result.order_number}`
            });
          } else {
            // 否则返回上一页
            uni.navigateBack();
          }
        }, 1500);

      } catch (error) {
        console.error('提交订单失败:', error);
        uni.hideLoading();

        let errorMessage = this.isStaffMode ? '预约创建失败，请重试' : '订单创建失败，请重试';
        if (error.message) {
          errorMessage = error.message;
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      } finally {
        this.submitting = false;
      }
    },

    // 表单验证
    validateForm() {
      // 检查联系人姓名
      if (!this.parsedInfo.name || !this.parsedInfo.name.trim()) {
        return { isValid: false, message: '请输入联系人姓名' };
      }

      // 检查联系方式
      if (!this.parsedInfo.phone || !this.parsedInfo.phone.trim()) {
        return { isValid: false, message: '请输入联系方式' };
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.parsedInfo.phone.trim())) {
        return { isValid: false, message: '请输入正确的手机号码' };
      }

      // 检查服务地址
      if (!this.parsedInfo.address || !this.parsedInfo.address.trim()) {
        return { isValid: false, message: '请选择或输入服务地址' };
      }

      // 检查选中的产品SKU
      if (!this.selectedSku || !this.selectedSku.skuId) {
        return { isValid: false, message: '请选择服务项目' };
      }

      // 检查购买金额
      if (!this.amount || parseFloat(this.amount) <= 0) {
        return { isValid: false, message: '请输入正确的购买金额' };
      }

      // 检查服务日期
      if (!this.serviceDate) {
        return { isValid: false, message: '请选择服务日期' };
      }

      // 检查服务时间
      if (!this.serviceTime) {
        return { isValid: false, message: '请选择服务时间' };
      }

      // 检查经纬度（如果有地址但没有经纬度，给出提示）
      if (!this.selectedLocation.latitude || !this.selectedLocation.longitude) {
        return { isValid: false, message: '请重新选择服务地址以获取准确位置' };
      }



      return { isValid: true, message: '' };
    },

    // 准备提交数据
    prepareSubmitData() {
      // 构建完整的服务地址
      let fullAddress = this.parsedInfo.address;
      if (this.doorNumber) {
        fullAddress += ` ${this.doorNumber}`;
      }

      // 验证必需的数据
      if (!this.selectedSku || !this.selectedSku.productUuid) {
        throw new Error('请先选择服务产品');
      }

      // 使用新的代客下单接口格式构建数据（后端会自动从current_user获取store_uuid）
      const submitData = {
        // 1. 基础订单信息
        order_info: {
          amount: parseFloat(this.amount) || 0, // 确保为数字类型
          buy_num: 1.0, // 确保为数字类型
          pay_type: '106', // 现金支付
          service_date: this.serviceDate, // 格式：YYYY-MM-DD
          service_time: this.serviceTime  // 格式：HH:MM
        },

        // 2. 客户信息
        customer_info: {
          name: this.parsedInfo.name,
          phone: this.parsedInfo.phone,
          original_input: this.customerInfo
        },

        // 3. 服务地址信息
        address_info: {
          address: fullAddress,
          door_number: this.doorNumber || '',
          longitude: parseFloat(this.selectedLocation.longitude) || 0, // 确保为数字类型
          latitude: parseFloat(this.selectedLocation.latitude) || 0,   // 确保为数字类型
          city: "厦门市",
          province: "福建省",
          district: ""
        },

        // 4. 产品SKU信息
        product_info: {
          product_uuid: this.selectedSku.productUuid,
          sku_id: parseInt(this.selectedSku.skuId) || 1, // 确保为整数类型
          sku_name: this.selectedSku.skuName || '',
          sku_price: parseFloat(this.selectedSku.skuPrice) || parseFloat(this.amount) || 0,
          sku_commission: parseFloat(this.selectedSku.skuCommission) || 0,
          sku_commission_type: parseInt(this.selectedSku.skuCommissionType) || 1
        },

        // 5. 服务备注信息
        remark_info: {
          service_remark: this.serviceNotice || '',
          customer_note: this.selectedTags['客户备注'] ? this.customerNote : '',
          after_sale_remark: this.selectedTags['售后备注'] ? this.afterSalesNote : ''
        },

        // 6. 扩展业务信息
        business_info: {
          sales_attribution: this.selectedTags['销售归属'] ? this.salesAttribution : '',
          sales_attribution_staff_id: this.selectedTags['销售归属'] && this.selectedSalesStaff ? this.selectedSalesStaff.uuid : '',
          sales_attribution_staff_name: this.selectedTags['销售归属'] && this.selectedSalesStaff ? (this.selectedSalesStaff.real_name || this.selectedSalesStaff.name) : '',
          sales_commission: this.selectedTags['销售归属'] && this.salesCommission ? this.salesCommission : '',
          channel_code: this.selectedTags['渠道编号'] ? this.channelCode : '',
          order_source: this.selectedTags['订单来源'] ? this.orderSource : '',
          area: this.selectedTags['面积'] ? this.area : '',
          house_type: this.selectedTags['户型'] ? this.houseType : ''
        }
      };

      return submitData;
    },

    // 提交订单到后端
    async submitOrder(orderData) {
      try {
        // 根据端类型检查登录状态
        if (this.isStaffMode) {
          // 员工端检查员工登录状态
          const staffToken = this.$store.state.staffToken || '';
          if (!staffToken) {
            throw new Error('员工未登录，请先登录');
          }
        } else {
          // 门店端检查管理员登录状态
          const token = this.$store.state.token || '';
          if (!token) {
            throw new Error('用户未登录，请先登录');
          }
        }

        // 手动将对象转换为JSON字符串
        const jsonData = JSON.stringify(orderData);
        console.log('发送的JSON数据:', jsonData);

        // 根据端类型选择不同的API接口
        const apiUrl = this.isStaffMode ? '/api/v1/staff-order/proxy-create' : '/api/v1/order/proxy-create';
        const result = await this.$post(apiUrl, jsonData, {
          contentType: 'application/json'
        });

        console.log(`${this.isStaffMode ? '员工代客预约' : '代客下单'}接口响应:`, result);
        return result;
      } catch (error) {
        console.error(`${this.isStaffMode ? '员工代客预约' : '代客下单'}接口请求失败:`, error);

        // 根据错误类型进行处理
        if (error.code) {
          const errorMsg = this.handleOrderError(error.code, error.message);
          throw new Error(errorMsg);
        } else {
          throw new Error(`网络请求失败: ${error.message || '未知错误'}`);
        }
      }
    },

    // 处理订单错误
    handleOrderError(code, message) {
      switch (code) {
        case 1001:
          return '参数验证失败，请检查输入信息';
        case 1002:
          return '门店信息异常，请联系管理员';
        case 1003:
          return '所选产品已下架，请重新选择';
        case 1004:
          return '所选规格不可用，请重新选择';
        case 1005:
          return '所选时间不在营业时间内';
        case 1006:
          return '当前时间段没有可用服务人员';
        case 1007:
          return '服务地址超出配送范围';
        case 1008:
          return '手机号格式不正确';
        case 1009:
          return '订单金额异常';
        case 1010:
          return '系统繁忙，请稍后重试';
        default:
          return message || (this.isStaffMode ? '预约创建失败' : '订单创建失败');
      }
    },

    // 获取门店信息
    async loadStoreInfo() {
      try {
        console.log(`${this.isStaffMode ? '员工端' : '门店端'}开始获取门店信息...`);

        if (this.isStaffMode) {
          // 员工端从当前选中的公司获取门店信息
          const selectedCompany = this.$store.getters.getCurrentSelectedCompany;
          if (selectedCompany) {
            this.storeInfo = selectedCompany;
            console.log('员工端获取门店信息成功:', this.storeInfo);

            // 更新营业时间（如果有的话）
            this.storeBusinessHours = this.storeInfo.business_hours || '08:00-18:00';
            console.log('门店营业时间:', this.storeBusinessHours);
          } else {
            console.warn('员工端未获取到门店信息，使用默认营业时间');
            this.storeBusinessHours = '08:00-18:00';
          }
        } else {
          // 门店端调用门店信息接口
          const response = await get('/api/v1/order/storeInfo');

          if (response && response.business_hours) {
            this.storeInfo = response;
            console.log('门店端获取门店信息成功:', this.storeInfo);

            // 更新营业时间
            this.storeBusinessHours = this.storeInfo.business_hours;
            console.log('门店营业时间:', this.storeBusinessHours);
          } else {
            console.warn('门店端获取门店信息失败，使用默认营业时间');
            this.storeBusinessHours = '08:00-18:00';
          }
        }

        // 根据营业时间更新时间选项
        this.updateTimeOptions();
      } catch (error) {
        console.error(`${this.isStaffMode ? '员工端' : '门店端'}获取门店信息异常:`, error);
        // 发生错误时使用默认时间选项
        this.storeBusinessHours = '08:00-18:00';
        this.updateTimeOptions();
      }
    },

    // 根据营业时间更新时间选项
    updateTimeOptions() {
      try {
        // 解析营业时间，格式如 "08:00-18:00"
        const businessHours = this.storeBusinessHours;
        const timeRange = businessHours.split('-');

        if (timeRange.length === 2) {
          const startTime = timeRange[0].trim();
          const endTime = timeRange[1].trim();

          // 解析开始和结束时间
          const startHour = parseInt(startTime.split(':')[0]);
          const endHour = parseInt(endTime.split(':')[0]);

          // 生成营业时间内的时间选项（每小时一个）
          const newTimeOptions = [];
          for (let hour = startHour; hour <= endHour; hour++) {
            newTimeOptions.push(`${hour.toString().padStart(2, '0')}:00`);
          }

          // 如果营业时间范围合理，使用新的时间选项
          if (newTimeOptions.length > 0) {
            this.timeOptions = newTimeOptions;
            console.log('更新时间选项成功:', this.timeOptions);
          } else {
            // 如果解析失败，使用默认的营业时间选项
            this.useDefaultTimeOptions();
          }
        } else {
          // 如果营业时间格式不正确，使用默认选项
          this.useDefaultTimeOptions();
        }
      } catch (error) {
        console.error('更新时间选项失败:', error);
        this.useDefaultTimeOptions();
      }
    },

    // 使用默认时间选项（营业时间 8:00-18:00）
    useDefaultTimeOptions() {
      this.timeOptions = [
        '08:00', '09:00', '10:00', '11:00', '12:00', '13:00',
        '14:00', '15:00', '16:00', '17:00', '18:00'
      ];
      console.log('使用默认时间选项:', this.timeOptions);
    },

    // 获取门店UUID
    getStoreUuid() {
      // 1. 优先从当前用户的门店信息获取
      if (this.$store.state.storeInfo && this.$store.state.storeInfo.store_uuid) {
        console.log('从storeInfo获取store_uuid:', this.$store.state.storeInfo.store_uuid);
        return this.$store.state.storeInfo.store_uuid;
      }

      // 2. 从本地存储的storeInfo获取
      try {
        const storeInfo = uni.getStorageSync('storeInfo');
        if (storeInfo && storeInfo.store_uuid) {
          console.log('从本地存储storeInfo获取store_uuid:', storeInfo.store_uuid);
          return storeInfo.store_uuid;
        }
      } catch (error) {
        console.error('获取本地存储storeInfo失败:', error);
      }

      // 3. 从本地存储的store_uuid获取
      try {
        const storeUuid = uni.getStorageSync('store_uuid');
        if (storeUuid) {
          console.log('从本地存储store_uuid获取:', storeUuid);
          return storeUuid;
        }
      } catch (error) {
        console.error('获取本地存储store_uuid失败:', error);
      }

      // 4. 如果都没有，抛出错误
      console.error('无法获取store_uuid，请检查登录状态');
      throw new Error('无法获取门店信息，请重新登录');
    },
  },
};
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

// 现代化头部样式
.header-section {
  position: relative;
  padding-bottom: 40rpx;
  margin-bottom: 30rpx;
  padding-top: calc(var(--status-bar-height) + 20rpx);

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 0;
  }
}

.top-nav {
  display: flex;
  align-items: center;
  height: 88rpx;
  position: relative;

  .nav-left {
    width: 88rpx;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .back-btn {
      width: 64rpx;
      height: 64rpx;
      border-radius: 32rpx;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0.95);
      }
    }
  }

  .nav-center {
    flex: 1;
    text-align: center;

    .nav-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #fff;
    }
  }

  .nav-right {
    width: 88rpx;
  }
}

.content {
  padding: 0 30rpx 40rpx;
}

// 统一卡片样式
.info-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;

  .card-header {
    display: flex;
    align-items: center;
    padding: 30rpx 30rpx 20rpx;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1rpx solid #f0f0f0;

    .header-icon {
      width: 64rpx;
      height: 64rpx;
      border-radius: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;

      &.identify-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.customer-icon {
        background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      }

      &.order-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.notice-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }

      &.extend-icon {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      }

      &.remark-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.tags-icon {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
      }

      &.tips-icon {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      }
    }

    .card-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      flex: 1;
    }

    .card-subtitle {
      font-size: 24rpx;
      color: #999;
      margin-left: 10rpx;
    }
  }

  .card-content {
    padding: 30rpx;
  }
}

// 识别卡片特殊样式
.identify-card {
  .info-text {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 24rpx;
    line-height: 1.6;
  }

  .input-area {
    position: relative;
    margin-bottom: 24rpx;

    textarea {
      width: 100%;
      height: 200rpx;
      background-color: #f8f9fa;
      border: 2rpx solid #e9ecef;
      border-radius: 12rpx;
      padding: 20rpx;
      padding-right: 80rpx; // 为语音按钮留出空间
      font-size: 28rpx;
      color: #333;
      transition: all 0.3s ease;

      &:focus {
        border-color: #fdd118;
        background-color: #fff;
      }
    }

    .voice-btn {
      position: absolute;
      right: 16rpx;
      bottom: 16rpx;
      width: 56rpx;
      height: 56rpx;
      background-color: #fff;
      border: 2rpx solid #e9ecef;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      z-index: 10;

      &:active {
        transform: scale(0.9);
      }

      &.recording {
        background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
        border-color: #ff4757;
        animation: pulse 1s infinite;
        box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.3);
      }
    }

    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.4);
      }
      70% {
        box-shadow: 0 0 0 20rpx rgba(255, 71, 87, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(255, 71, 87, 0);
      }
    }

    .counter {
      position: absolute;
      left: 20rpx;
      bottom: 20rpx;
      font-size: 24rpx;
      color: #999;
      background: rgba(255, 255, 255, 0.9);
      padding: 4rpx 8rpx;
      border-radius: 8rpx;
      z-index: 5;
    }
  }

  .identify-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    height: 72rpx;
    padding: 0 32rpx;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    color: #fff;
    border-radius: 36rpx;
    font-size: 28rpx;
    font-weight: 500;
    margin-left: auto;
    box-shadow: 0 4rpx 16rpx rgba(253, 209, 24, 0.3);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.4);
    }
  }
}

// 表单样式
.form-section {
  padding: 0;

  .form-item {
    display: flex;
    align-items: center;
    min-height: 100rpx;
    padding: 0 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .item-label {
      width: 200rpx;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;

      &.required::before {
        content: '*';
        color: #ff4757;
        margin-right: 6rpx;
        font-weight: bold;
      }
    }

    .item-content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 80rpx;

      input {
        flex: 1;
        font-size: 28rpx;
        color: #333;

        &::placeholder {
          color: #999;
        }
      }

      .placeholder {
        color: #999;
        font-size: 28rpx;
      }

      .parsed-info {
        color: #333;
        font-size: 28rpx;
        font-weight: 500;
        background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      // 销售提成显示样式
      &.commission-display {
        .commission-amount {
          font-size: 28rpx;
          color: #ff6b35;
          font-weight: 600;
        }

        .commission-note {
          font-size: 24rpx;
          color: #999;
          margin-left: 8rpx;
        }
      }

      .selected-product {
        flex: 1;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 16rpx;

        .product-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 8rpx;

          .product-name {
            font-size: 28rpx;
            color: #333;
            font-weight: 600;
            line-height: 1.3;
          }

          .product-details {
            display: flex;
            align-items: center;
            gap: 12rpx;
            flex-wrap: wrap;

            .product-category {
              font-size: 24rpx;
              color: #fdd118;
              font-weight: 500;
            }

            .product-type {
              font-size: 22rpx;
              color: #666;
              background: #f0f0f0;
              padding: 2rpx 8rpx;
              border-radius: 8rpx;
            }
          }

          .product-notes {
            font-size: 22rpx;
            color: #999;
            line-height: 1.4;
            margin-top: 4rpx;
          }
        }

        .product-badges {
          display: flex;
          flex-direction: column;
          gap: 8rpx;
          align-items: flex-end;

          .product-badge {
            padding: 4rpx 12rpx;
            background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
            border-radius: 12rpx;

            .badge-text {
              font-size: 20rpx;
              color: #fff;
              font-weight: 600;
            }

            &.type-badge {
              background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            }
          }
        }
      }
    }
  }
}

// 地图预览样式
.map-preview-container {
  margin: 24rpx 0;
  border-radius: 16rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);

  .map-preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 32rpx;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;

    .map-preview-title {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }

    .map-preview-actions {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .map-action-btn {
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 8rpx 16rpx;
        background: #007AFF;
        border-radius: 8rpx;
        font-size: 24rpx;
        color: #fff;

        .action-text {
          font-size: 24rpx;
          color: #fff;
        }
      }
    }
  }

  .map-preview-content {
    position: relative;
    height: 300rpx;
    overflow: hidden;

    .preview-map {
      width: 100%;
      height: 100%;
    }

    .map-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
      padding: 32rpx 24rpx 24rpx;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;

      .location-info {
        flex: 1;
        color: #fff;

        .location-address {
          display: block;
          font-size: 26rpx;
          font-weight: 500;
          margin-bottom: 8rpx;
          line-height: 1.3;
        }

        .location-coords {
          display: block;
          font-size: 22rpx;
          opacity: 0.8;
        }
      }

      .map-tip {
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 8rpx 12rpx;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 16rpx;
        backdrop-filter: blur(10rpx);

        .tip-text {
          font-size: 22rpx;
          color: #fff;
        }
      }
    }
  }
}

// 备注输入样式
.notice-input, .remark-input {
  position: relative;

  textarea {
    width: 100%;
    height: 160rpx;
    background-color: #f8f9fa;
    border: 2rpx solid #e9ecef;
    border-radius: 12rpx;
    padding: 20rpx;
    font-size: 28rpx;
    color: #333;
    transition: all 0.3s ease;

    &:focus {
      border-color: #fdd118;
      background-color: #fff;
    }

    &::placeholder {
      color: #999;
    }
  }

  .counter {
    position: absolute;
    right: 20rpx;
    bottom: 20rpx;
    font-size: 24rpx;
    color: #999;
    background: rgba(255, 255, 255, 0.9);
    padding: 4rpx 8rpx;
    border-radius: 8rpx;
  }
}

// 备注卡片样式
.remarks-card {
  .remark-section {
    margin-bottom: 30rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .remark-title {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      margin-bottom: 16rpx;
    }
  }
}

// 标签选择样式
.tags-section {
  .tags-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    // 最后一行居中显示
    &.tags-row-center {
      justify-content: center;
      gap: 24rpx;
    }
  }

  .tag-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    padding: 24rpx 16rpx;
    background-color: #f8f9fa;
    border: 2rpx solid #e9ecef;
    border-radius: 16rpx;
    color: #666;
    transition: all 0.3s ease;
    flex: 1;
    margin: 0 8rpx;
    min-height: 120rpx;
    position: relative;
    overflow: hidden;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    // 在居中行中的标签样式
    .tags-row-center & {
      flex: 0 0 200rpx;
      margin: 0 12rpx;
    }

    .tag-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    .tag-text {
      font-size: 26rpx;
      font-weight: 500;
      text-align: center;
      line-height: 1.2;
      transition: all 0.3s ease;
    }

    // 悬停效果
    &:not(.active):hover {
      background-color: #e9ecef;
      border-color: #dee2e6;
    }

    &.active {
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      border-color: #fdd118;
      color: #fff;
      transform: translateY(-4rpx);
      box-shadow: 0 8rpx 20rpx rgba(253, 209, 24, 0.3);

      .tag-icon {
        transform: scale(1.1);
      }

      .tag-text {
        font-weight: 600;
      }

      // 添加光泽效果
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
      }

      &:active::before {
        left: 100%;
      }
    }

    &:active {
      transform: scale(0.96) translateY(-2rpx);
    }
  }
}

// 提示卡片样式
.tips-card {
  .tips-list {
    .tips-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16rpx;
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;

      &:last-child {
        margin-bottom: 0;
      }

      .tips-dot {
        width: 8rpx;
        height: 8rpx;
        background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
        border-radius: 50%;
        margin-top: 12rpx;
        margin-right: 16rpx;
        flex-shrink: 0;
      }
    }
  }
}

// 底部操作区域
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;

  .submit-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    color: #fff;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    font-size: 32rpx;
    font-weight: 600;
    box-shadow: 0 8rpx 24rpx rgba(253, 209, 24, 0.4);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 4rpx 16rpx rgba(253, 209, 24, 0.5);
    }

    &.disabled {
      opacity: 0.6;
      pointer-events: none;
    }

    &.external-customer {
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
      box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.4);

      &:active {
        box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.5);
      }
    }
  }
}



// 时间选择器样式
.time-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
}

.picker-content {
  width: 100%;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
  max-height: 80vh;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-cancel {
  color: #999;
  font-size: 32rpx;
}

.picker-title {
  color: #333;
  font-size: 34rpx;
  font-weight: 600;
}

.picker-confirm {
  color: #fdd118;
  font-size: 32rpx;
  font-weight: 600;
}

.time-options-container {
  height: 600rpx;
}

.time-options-scroll {
  height: 100%;
}

.time-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: all 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: #f8f9fa;
  }

  &.selected {
    background: #fff7e6;
    border-color: #fdd118;

    text {
      color: #fdd118;
      font-weight: 600;
    }
  }

  text {
    color: #333;
    font-size: 32rpx;
  }
}

// 支付方式选择样式
.payment-methods {
  display: flex;
  gap: 24rpx;
  margin-top: 8rpx;
}

.payment-method {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &.active {
    background: #fff7e6;
    border-color: #fdd118;

    .method-name {
      color: #fdd118;
      font-weight: 600;
    }
  }

  .method-icon {
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12rpx;

    &.cash-icon {
      background: #28a745;
    }

    &.balance-icon {
      background: #007AFF;
    }
  }

  .method-name {
    font-size: 28rpx;
    color: #666;
    transition: all 0.3s ease;
  }
}

// 余额信息样式
.balance-info {
  .balance-display {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .balance-amount {
      font-size: 32rpx;
      font-weight: 600;
      color: #007AFF;
    }

    .refresh-btn {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 8rpx 16rpx;
      background: #f0f8ff;
      border-radius: 8rpx;

      .refresh-text {
        font-size: 24rpx;
        color: #007AFF;
      }
    }
  }

  // 余额不足提示已改为弹窗形式，不再需要固定样式
}

// 禁用状态通用样式
.disable {
  opacity: 0.5;
  pointer-events: none;
}
</style>

