<template>
  <view class="page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 现代化头部 -->
    <view class="header-section">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 顶部导航 -->
        <view class="top-nav">
          <view class="nav-left" @click="handleBack">
            <view class="back-btn">
              <u-icon name="arrow-left" color="#fff" size="20"></u-icon>
            </view>
          </view>
          <view class="nav-center">
            <text class="nav-title">产品价格表</text>
          </view>
          <view class="nav-right"></view>
        </view>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-box">
        <u-icon name="search" color="#999" size="16"></u-icon>
        <input 
          type="text" 
          v-model="searchKeyword" 
          placeholder="搜索产品名称" 
          placeholder-class="search-placeholder"
          @input="handleSearchInput"
        />
        <view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
          <u-icon name="close-circle-fill" color="#ccc" size="16"></u-icon>
        </view>
      </view>
    </view>

    <!-- 分类导航 -->
    <view v-if="categoryList.length > 1" class="category-nav">
      <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="false">
        <view class="category-tabs">
          <view
            v-for="category in categoryList"
            :key="category"
            class="category-tab"
            :class="{ 'active': selectedCategory === category }"
            @click="handleCategoryChange(category)"
          >
            <view class="category-icon">
              <u-icon :name="getCategoryIcon(category)" :color="selectedCategory === category ? '#fff' : '#fdd118'" size="16"></u-icon>
            </view>
            <text class="category-text">{{ category }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 产品列表 -->
    <view class="content">
      <!-- 加载状态 -->
      <view v-if="loading && filteredProductList.length === 0" class="loading-container">
        <u-loading-icon mode="spinner" color="#fdd118" size="24"></u-loading-icon>
        <text class="loading-text">正在加载产品列表...</text>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!loading && filteredProductList.length === 0" class="empty-container">
        <text class="empty-text">暂无可购买的产品</text>
        <text class="empty-desc">请联系管理员添加产品或购买相关版本</text>
      </view>

      <!-- 产品列表 -->
      <view v-else class="product-list">
        <view
          v-for="product in filteredProductList"
          :key="product.id"
          class="product-card"
          :class="{ 'own-product': product.is_own_product }"
          @click="handleProductClick(product)"
        >
          <!-- 产品卡片内容 -->
          <view class="product-content">
            <!-- 产品图片 -->
            <view class="product-image">
              <image
                :src="getProductImage(product)"
                mode="aspectFill"
                class="product-img"
                @error="handleImageError"
              ></image>
              <!-- 状态标签 -->
              <view class="status-overlay">
                <view class="status-dot" :class="{ 'active': product.product_status === 1 }"></view>
              </view>
            </view>

            <!-- 产品信息 -->
            <view class="product-info">
              <!-- 产品标题和标签 -->
              <view class="product-header">
                <text class="product-name">{{ product.product_name }}</text>
                <view class="product-badges">
                  <view v-if="product.is_own_product" class="badge own-badge">
                    <u-icon name="star-fill" size="10" color="#fff"></u-icon>
                    <text>本公司</text>
                  </view>
                  <view class="badge type-badge" :class="{
                    'service-type': product.type === 'service',
                    'product-type': product.type === 'product',
                    'single-type': product.type === '1',
                    'package-type': product.type === '2'
                  }">
                    <text>{{ product.type_name }}</text>
                  </view>
                </view>
              </view>

              <!-- 产品分类 -->
              <view class="product-category">
                <text class="category-text">{{ product.service_skill_name }}</text>
                <text v-if="product.service_skill_main_name" class="main-category-text">
                  · {{ product.service_skill_main_name }}
                </text>
              </view>

              <!-- 产品描述 -->
              <view v-if="product.details" class="product-description">
                <text class="description-text">{{ truncateText(product.details, 60) }}</text>
              </view>

              <!-- 产品规格信息 -->
              <view class="product-specs">
                <view v-if="product.buy_notes" class="spec-item">
                  <text class="spec-text">{{ truncateText(product.buy_notes, 40) }}</text>
                </view>
              </view>

              <!-- SKU选项 -->
              <view class="product-skus" v-if="product.skus && product.skus.length > 0">
                <view class="sku-title">
                  <text>选择规格：</text>
                </view>
                <view class="sku-list">
                  <view
                    v-for="sku in product.skus"
                    :key="sku.id"
                    class="sku-item"
                    @click.stop="handleSelectSku(product, sku)"
                  >
                    <view class="sku-info">
                      <text class="sku-name">{{ sku.name }}</text>
                      <text class="sku-price">￥{{ sku.now_price }}</text>
                    </view>
                    <view class="sku-select-btn">
                      <u-icon name="checkmark" size="14" color="#fff"></u-icon>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 无SKU时的提示 -->
              <view v-else class="no-sku-tip">
                <text>暂无可选规格</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && !loading" class="load-more" @click="loadMore">
        <text class="load-more-text">加载更多</text>
        <u-icon name="arrow-down" color="#666" size="14"></u-icon>
      </view>

      <!-- 加载中 -->
      <view v-if="loading && productList.length > 0" class="loading-more">
        <u-loading-icon mode="spinner" color="#fdd118" size="16"></u-loading-icon>
        <text class="loading-more-text">加载中...</text>
      </view>

      <!-- 没有更多 -->
      <view v-if="!hasMore && productList.length > 0" class="no-more">
        <text class="no-more-text">已显示全部产品</text>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { getPurchasableProducts, getPurchasableProductCategories, getPurchasableProductsWithCategories } from '@/api/product.js'

export default {
  computed: {
    ...mapState(['StatusBar']),

    // 根据端类型获取当前公司信息
    currentCompanyInfo() {
      if (this.isStaffMode) {
        // 员工端：从当前选中的公司获取
        return this.$store.getters.getCurrentSelectedCompany;
      } else {
        // 门店端：从门店信息获取
        return this.$store.state.storeInfo;
      }
    }
  },

  data() {
    return {
      // 端类型识别
      isStaffMode: false, // 是否为员工端模式

      searchKeyword: '', // 搜索关键词
      productList: [], // 原始产品列表
      categorizedProducts: {}, // 分类后的产品列表
      filteredProductList: [], // 过滤后的产品列表
      selectedCategory: '全部', // 当前选中的分类
      categoryList: ['全部'], // 分类列表
      loading: false, // 加载状态
      currentPage: 1, // 当前页码
      pageSize: 20, // 每页数量
      total: 0, // 总数
      hasMore: true, // 是否有更多数据
      searchTimer: null, // 搜索防抖定时器
      // 缓存相关
      cacheKey: '',
      cacheData: null,
      cacheExpiry: 5 * 60 * 1000, // 5分钟缓存
      lastLoadTime: 0,
      categoryIcons: { // 分类图标映射 - 统一方格风格
        '全部': 'grid',
        '维修安装': 'grid',
        '保洁清洗': 'grid',
        '家电维修': 'grid',
        '管道疏通': 'grid',
        '搬家服务': 'grid',
        '月嫂育儿': 'grid',
        '养老护理': 'grid',
        '其他服务': 'grid'
      }
    };
  },

  onLoad(options) {
    console.log('产品列表页面接收到的参数:', options);

    // 识别是否为员工端模式
    this.isStaffMode = options.from === 'staff';
    console.log('产品列表页面当前模式:', this.isStaffMode ? '员工端' : '门店端');

    // 使用优化版API，一次性加载分类和产品
    this.loadProductsWithCategories();
  },

  methods: {
    // 返回上一页
    handleBack() {
      uni.navigateBack();
    },

    // 根据端类型调用合适的产品API
    async callProductAPI(params) {
      try {
        if (this.isStaffMode) {
          // 员工端：确保使用员工身份调用API
          console.log('员工端调用产品API，确保使用员工token');

          // 临时设置当前角色为员工，确保使用正确的token
          const originalRole = uni.getStorageSync('currentRole');
          uni.setStorageSync('currentRole', 'staff');
          this.$store.commit('Updates', { currentRole: 'staff' });

          try {
            const response = await getPurchasableProductsWithCategories(params);
            return response;
          } finally {
            // 恢复原来的角色设置
            if (originalRole) {
              uni.setStorageSync('currentRole', originalRole);
              this.$store.commit('Updates', { currentRole: originalRole });
            }
          }
        } else {
          // 门店端：正常调用
          console.log('门店端调用产品API');
          return await getPurchasableProductsWithCategories(params);
        }
      } catch (error) {
        console.error(`${this.isStaffMode ? '员工端' : '门店端'}产品API调用失败:`, error);
        throw error;
      }
    },

    // 优化版：一次性加载产品和分类信息（带性能监控和重试）
    async loadProductsWithCategories(retryCount = 0) {
      const startTime = Date.now();
      const maxRetries = 2;

      try {
        console.log(`${this.isStaffMode ? '员工端' : '门店端'}开始加载产品和分类信息... (尝试 ${retryCount + 1}/${maxRetries + 1})`);
        this.loading = true;

        const params = {
          page: this.currentPage,
          size: this.pageSize,
          keyword: this.searchKeyword || undefined
        };

        // 根据端类型调用API
        const response = await this.callProductAPI(params);

        if (response && response.products && response.categories) {
          // 处理分类数据
          const categories = ['全部'];
          response.categories.forEach(category => {
            if (category.name && category.name !== '全部') {
              categories.push(category.name);
            }
          });
          this.categoryList = categories;

          // 处理产品数据
          if (this.currentPage === 1) {
            this.productList = response.products;
          } else {
            this.productList = [...this.productList, ...response.products];
          }

          // 处理分页信息
          this.total = response.pagination.total;
          this.hasMore = this.currentPage < response.pagination.pages;

          // 缓存数据
          if (this.currentPage === 1) {
            this.cacheData = {
              key: this.cacheKey,
              data: {
                products: this.productList,
                categories: response.categories,
                total: this.total,
                hasMore: this.hasMore
              }
            };
            this.lastLoadTime = Date.now();
          }

          // 处理分类数据
          this.processCategorizedData();

          const loadTime = Date.now() - startTime;
          console.log('产品和分类加载成功:', {
            categories: this.categoryList.length,
            products: this.productList.length,
            total: this.total,
            loadTime: `${loadTime}ms`
          });
        } else {
          throw new Error('数据格式异常');
        }
      } catch (error) {
        const loadTime = Date.now() - startTime;
        console.error(`${this.isStaffMode ? '员工端' : '门店端'}加载产品和分类失败 (${loadTime}ms):`, error);

        // 特殊处理员工端认证错误
        if (this.isStaffMode && (error.code === 401 || error.message?.includes('Not authenticated'))) {
          console.log('员工端认证失败，尝试重新设置角色和token');

          // 确保员工端角色设置正确
          uni.setStorageSync('currentRole', 'staff');
          this.$store.commit('Updates', { currentRole: 'staff' });

          // 如果是第一次重试，尝试重新认证
          if (retryCount === 0) {
            console.log('员工端认证失败，1秒后重试...');
            setTimeout(() => {
              this.loadProductsWithCategories(retryCount + 1);
            }, 1000);
            return;
          }
        }

        // 重试机制
        if (retryCount < maxRetries) {
          console.log(`${retryCount + 1}秒后重试...`);
          setTimeout(() => {
            this.loadProductsWithCategories(retryCount + 1);
          }, (retryCount + 1) * 1000);
          return;
        }

        // 最终失败时使用备用方案
        if (this.currentPage === 1) {
          console.log('使用备用方案加载数据...');
          await this.loadCategoriesFallback();
          await this.loadProductListFallback(true); // 传递isRefresh=true
        } else {
          const errorMsg = this.isStaffMode ? '员工端加载失败，请重试' : '加载失败，请重试';
          uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
          });
        }
      } finally {
        this.loading = false;
      }
    },

    // 备用方案：分别加载分类和产品
    async loadCategoriesFallback() {
      try {
        console.log('使用备用方案加载产品分类...');

        const response = await getPurchasableProductCategories();

        if (response && Array.isArray(response)) {
          const categories = ['全部'];
          response.forEach(category => {
            if (category.name && category.name !== '全部') {
              categories.push(category.name);
            }
          });
          this.categoryList = categories;
        } else {
          this.categoryList = ['全部', '其他服务'];
        }
      } catch (error) {
        console.error('备用分类加载失败:', error);
        this.categoryList = ['全部', '其他服务'];
      }
    },

    // 备用方案：加载产品列表
    async loadProductListFallback(isRefresh = false) {
      if (this.loading) return;

      try {
        this.loading = true;

        if (isRefresh) {
          this.currentPage = 1;
          this.productList = [];
        }

        const params = {
          page: this.currentPage,
          size: this.pageSize
        };

        if (this.searchKeyword && this.searchKeyword.trim()) {
          params.search_keyword = this.searchKeyword.trim();
        }

        const response = await getPurchasableProducts(params);

        if (response && response.list) {
          if (isRefresh) {
            this.productList = response.list;
          } else {
            this.productList = [...this.productList, ...response.list];
          }

          this.total = response.total;
          this.hasMore = response.has_more;

          // 处理分类数据
          this.processCategorizedData();

          if (response.list.length > 0) {
            this.currentPage++;
          }
        }

      } catch (error) {
        console.error('备用产品列表加载失败:', error);
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none',
          duration: 2000
        });
      } finally {
        this.loading = false;
      }
    },

    // 优化版：加载更多产品
    async loadMore() {
      if (this.loading || !this.hasMore) {
        return;
      }

      this.currentPage++;

      // 使用优化版API加载更多
      try {
        this.loading = true;

        const params = {
          page: this.currentPage,
          size: this.pageSize,
          keyword: this.searchKeyword || undefined
        };

        const response = await getPurchasableProductsWithCategories(params);

        if (response && response.products) {
          // 追加产品数据
          this.productList = [...this.productList, ...response.products];

          // 更新分页信息
          this.hasMore = this.currentPage < response.pagination.pages;

          // 重新处理分类数据
          this.processCategorizedData();

          console.log(`加载更多成功，当前页: ${this.currentPage}, 产品总数: ${this.productList.length}`);
        }
      } catch (error) {
        console.error('加载更多失败，使用备用方案:', error);
        // 回退页码
        this.currentPage--;

        // 使用备用方案（不刷新，追加数据）
        await this.loadProductListFallback(false);
      } finally {
        this.loading = false;
      }
    },

    // 优化版：搜索输入处理（防抖 + 缓存）
    handleSearchInput() {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置新的定时器，300ms后执行搜索（减少延迟）
      this.searchTimer = setTimeout(() => {
        this.performSearch();
      }, 300);
    },

    // 执行搜索
    async performSearch() {
      // 重置分页
      this.currentPage = 1;
      this.productList = [];
      this.hasMore = true;

      // 生成缓存键
      const searchKey = this.searchKeyword.trim();
      this.cacheKey = `search_${searchKey}_${this.selectedCategory}`;

      // 检查缓存
      if (this.cacheData && this.cacheData.key === this.cacheKey) {
        const now = Date.now();
        if (now - this.lastLoadTime < this.cacheExpiry) {
          console.log('使用缓存数据');
          this.applySearchResults(this.cacheData.data);
          return;
        }
      }

      // 执行搜索
      await this.loadProductsWithCategories();
    },

    // 应用搜索结果
    applySearchResults(data) {
      this.productList = data.products || [];
      this.categoryList = ['全部', ...(data.categories || [])];
      this.total = data.total || 0;
      this.hasMore = data.hasMore || false;
      this.processCategorizedData();
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = '';
      this.cacheKey = '';
      this.cacheData = null;
      this.performSearch();
    },

    // 处理分类数据
    processCategorizedData() {
      const categorized = {};

      // 初始化所有预加载的分类
      this.categoryList.forEach(categoryName => {
        if (categoryName !== '全部') {
          categorized[categoryName] = {
            products: [],
            expanded: true // 默认展开
          };
        }
      });

      // 将产品分配到对应分类
      this.productList.forEach(product => {
        const categoryName = product.service_skill_main_name || '其他服务';

        if (!categorized[categoryName]) {
          categorized[categoryName] = {
            products: [],
            expanded: true
          };
        }

        categorized[categoryName].products.push(product);
      });

      // 按分类名称排序
      const sortedCategories = {};
      Object.keys(categorized).sort().forEach(key => {
        sortedCategories[key] = categorized[key];
      });

      this.categorizedProducts = sortedCategories;

      // 更新过滤后的产品列表
      this.updateFilteredProducts();
    },

    // 更新过滤后的产品列表
    updateFilteredProducts() {
      if (this.selectedCategory === '全部') {
        this.filteredProductList = this.productList;
      } else {
        this.filteredProductList = this.productList.filter(product => {
          const categoryName = product.service_skill_main_name || '其他服务';
          return categoryName === this.selectedCategory;
        });
      }
    },

    // 处理分类切换
    handleCategoryChange(category) {
      this.selectedCategory = category;
      this.updateFilteredProducts();
    },

    // 获取分类图标
    getCategoryIcon(categoryName) {
      return this.categoryIcons[categoryName] || 'grid';
    },

    // 获取产品图片
    getProductImage(product) {
      // 优先使用后端返回的完整图片URL
      if (product.cover_image_url) {
        return product.cover_image_url;
      }
      // 如果有图片列表，使用第一张图片
      if (product.images && product.images.length > 0) {
        return product.images[0].url;
      }
      // 如果有图片ID，构建图片URL（向后兼容）
      if (product.img_id) {
        return `/api/v1/common/file/download/${product.img_id}`;
      }
      // 返回默认图片（使用现有的logo作为占位符）
      return '/static/logo.png';
    },

    // 处理图片加载错误
    handleImageError(e) {
      // 设置默认图片
      e.target.src = '/static/logo.png';
    },

    // 截断文本
    truncateText(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    },

    // 获取类型徽章样式
    getTypeBadgeClass(type) {
      const typeClasses = {
        'service': 'service-type',
        'product': 'product-type',
        '1': 'single-type',
        '2': 'package-type'
      };
      return typeClasses[type] || 'default-type';
    },

    // 选择SKU
    handleSelectSku(product, sku) {
      console.log('选择SKU:', product, sku);

      // 构建SKU数据
      const selectedSkuData = {
        // SKU基本信息
        skuId: sku.id,
        skuName: sku.name,
        skuPrice: sku.now_price,
        skuVipPrice: sku.vip_price,
        skuDuration: sku.duration,
        skuPriceUnit: sku.type_price_unit,
        skuCommission: sku.define_commission,
        skuCommissionType: sku.commission_type,

        // 产品基本信息
        productId: product.id,
        productUuid: product.uuid,
        productName: product.product_name,
        category: product.service_skill_name,
        mainCategory: product.service_skill_main_name,
        type: product.type_name,
        minNumber: product.min_number,
        maxNumber: product.max_number,
        buyNotes: product.buy_notes,
        buyAgreement: product.buy_agreement,
        isOwnProduct: product.is_own_product,
        coverImageUrl: product.cover_image_url,
        images: product.images || []
      };

      // 使用存储方式传递数据（更可靠）
      uni.setStorageSync('selectedSku', selectedSkuData);

      // 同时保留事件总线作为备用机制
      uni.$emit('skuSelected', selectedSkuData);

      uni.showToast({
        title: `已选择: ${sku.name}`,
        icon: 'success',
        duration: 1000
      });

      // 立即返回代客下单页面
      setTimeout(() => {
        uni.navigateBack({
          delta: 1
        });
      }, 800);
    },

    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.loadProductList();
      }
    },

    // 产品点击
    handleProductClick(product) {
      console.log('选择产品:', product);
      uni.showToast({
        title: `选择了产品: ${product.product_name}`,
        icon: 'none',
        duration: 2000
      });
    },

    // 查看详情
    handleViewDetail(product) {
      console.log('查看产品详情:', product);
      
      let detailText = `产品名称: ${product.product_name}\n`;
      detailText += `服务类型: ${product.service_skill_name}\n`;
      if (product.service_skill_main_name) {
        detailText += `主要类型: ${product.service_skill_main_name}\n`;
      }
      detailText += `产品类型: ${product.type_name}\n`;
      detailText += `状态: ${product.product_status === 1 ? '可用' : '不可用'}\n`;
      if (product.min_number || product.max_number) {
        detailText += `数量范围: ${product.min_number || 0} - ${product.max_number || '不限'}\n`;
      }
      detailText += `来源: ${product.is_own_product ? '本公司产品' : '模板产品'}`;

      uni.showModal({
        title: '产品详情',
        content: detailText,
        showCancel: false,
        confirmText: '确定'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

// 现代化头部样式
.header-section {
  position: relative;
  padding-bottom: 40rpx;
  margin-bottom: 20rpx;
  padding-top: calc(var(--status-bar-height) + 20rpx);

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 0;
  }
}

.top-nav {
  display: flex;
  align-items: center;
  height: 88rpx;
  position: relative;

  .nav-left {
    width: 88rpx;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .back-btn {
      width: 64rpx;
      height: 64rpx;
      border-radius: 32rpx;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0.95);
      }
    }
  }

  .nav-center {
    flex: 1;
    text-align: center;

    .nav-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #fff;
    }
  }

  .nav-right {
    width: 88rpx;
  }
}

// 搜索栏样式
.search-section {
  padding: 0 30rpx 20rpx;
  margin-top: -20rpx;
  position: relative;
  z-index: 3;

  .search-box {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 50rpx;
    padding: 20rpx 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    gap: 20rpx;

    input {
      flex: 1;
      font-size: 28rpx;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }

    .clear-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32rpx;
      height: 32rpx;
    }
  }
}

// 分类导航样式
.category-nav {
  padding: 20rpx 0;
  background: #fff;
  margin: 0 30rpx 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .category-scroll {
    width: 100%;
  }

  .category-tabs {
    display: flex;
    padding: 0 20rpx;
    gap: 16rpx;

    .category-tab {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-width: 120rpx;
      padding: 16rpx 20rpx;
      border-radius: 12rpx;
      background: #f8f9fa;
      transition: all 0.3s ease;
      white-space: nowrap;

      &.active {
        background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
        transform: translateY(-2rpx);
        box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.3);
      }

      &:active {
        transform: scale(0.95);
      }

      .category-icon {
        margin-bottom: 8rpx;
      }

      .category-text {
        font-size: 24rpx;
        color: #666;
        font-weight: 500;
      }

      &.active .category-text {
        color: #fff;
        font-weight: 600;
      }
    }
  }
}

.content {
  padding: 0 30rpx;
}

// 加载和空状态样式
.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;

  .loading-text, .empty-text {
    font-size: 28rpx;
    color: #666;
    margin-top: 20rpx;
  }

  .empty-desc {
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
    line-height: 1.5;
  }
}

// 分类产品列表样式
.categorized-product-list {
  .category-section {
    margin-bottom: 32rpx;

    .category-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 30rpx;
      background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
      border-radius: 16rpx;
      margin-bottom: 16rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
      }

      .category-info {
        display: flex;
        align-items: center;
        gap: 20rpx;

        .category-icon {
          width: 64rpx;
          height: 64rpx;
          background: linear-gradient(135deg, rgba(253, 209, 24, 0.1) 0%, rgba(255, 128, 27, 0.1) 100%);
          border-radius: 32rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .category-details {
          .category-name {
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
            display: block;
            margin-bottom: 4rpx;
          }

          .category-count {
            font-size: 24rpx;
            color: #999;
          }
        }
      }

      .category-toggle {
        transition: transform 0.3s ease;

        &.rotated {
          transform: rotate(180deg);
        }
      }
    }

    .category-products {
      .product-card {
        background: #fff;
        border-radius: 16rpx;
        margin-bottom: 16rpx;
        padding: 24rpx;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
        border: 2rpx solid transparent;
        transition: all 0.3s ease;

        &:active {
          transform: translateY(-2rpx);
          box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
        }

        &.own-product {
          border-color: rgba(253, 209, 24, 0.3);
          background: linear-gradient(135deg, #fff 0%, rgba(253, 209, 24, 0.02) 100%);
        }

        .product-main {
          .product-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 16rpx;

            .product-title-section {
              flex: 1;

              .product-name {
                font-size: 30rpx;
                font-weight: 600;
                color: #333;
                line-height: 1.4;
                margin-bottom: 12rpx;
              }

              .product-badges {
                display: flex;
                gap: 12rpx;

                .badge {
                  display: flex;
                  align-items: center;
                  gap: 6rpx;
                  padding: 6rpx 12rpx;
                  border-radius: 16rpx;
                  font-size: 20rpx;

                  &.own-badge {
                    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
                    color: #fff;
                  }

                  &.type-badge {
                    background: #f0f0f0;
                    color: #666;

                    &.service-type {
                      background: rgba(52, 199, 89, 0.1);
                      color: #34c759;
                    }

                    &.product-type {
                      background: rgba(255, 149, 0, 0.1);
                      color: #ff9500;
                    }

                    &.single-type {
                      background: rgba(0, 122, 255, 0.1);
                      color: #007AFF;
                    }

                    &.package-type {
                      background: rgba(255, 59, 48, 0.1);
                      color: #ff3b30;
                    }
                  }
                }
              }
            }

            .product-status {
              .status-dot {
                width: 16rpx;
                height: 16rpx;
                border-radius: 50%;
                background: #ddd;

                &.active {
                  background: #52c41a;
                  box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.2);
                }
              }
            }
          }

          .product-meta {
            display: flex;
            gap: 24rpx;

            .meta-item {
              display: flex;
              align-items: center;
              gap: 8rpx;

              .meta-text {
                font-size: 24rpx;
                color: #666;
              }
            }
          }
        }

        .product-actions {
          display: flex;
          gap: 16rpx;
          margin-top: 20rpx;
          padding-top: 20rpx;
          border-top: 1rpx solid #f0f0f0;

          .action-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8rpx;
            padding: 16rpx 24rpx;
            border-radius: 12rpx;
            font-size: 26rpx;
            font-weight: 500;
            transition: all 0.3s ease;

            &.primary-btn {
              background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
              color: #fff;
              box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.3);

              &:active {
                transform: scale(0.95);
                box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.4);
              }
            }

            &.secondary-btn {
              background: rgba(0, 122, 255, 0.1);
              color: #007AFF;

              &:active {
                background: rgba(0, 122, 255, 0.2);
              }
            }
          }
        }
      }
    }
  }
}

// 产品列表样式
.product-list {
  .product-card {
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    border: 2rpx solid transparent;
    transition: all 0.3s ease;

    &:active {
      transform: translateY(-4rpx);
      box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);
    }

    &.own-product {
      border-color: rgba(253, 209, 24, 0.4);
      background: linear-gradient(135deg, #fff 0%, rgba(253, 209, 24, 0.03) 100%);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 6rpx;
        height: 100%;
        background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      }
    }

    .product-content {
      display: flex;
      padding: 24rpx;
      gap: 20rpx;

      .product-image {
        position: relative;
        width: 160rpx;
        height: 160rpx;
        border-radius: 16rpx;
        overflow: hidden;
        flex-shrink: 0;
        background: #f8f9fa;

        .product-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .status-overlay {
          position: absolute;
          top: 12rpx;
          right: 12rpx;

          .status-dot {
            width: 20rpx;
            height: 20rpx;
            border-radius: 50%;
            background: #ddd;
            border: 3rpx solid #fff;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

            &.active {
              background: #52c41a;
              box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
            }
          }
        }

        &:active .product-img {
          transform: scale(1.05);
        }
      }

      .product-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        min-height: 160rpx;

        .product-header {
          .product-name {
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
            line-height: 1.4;
            margin-bottom: 12rpx;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .product-badges {
            display: flex;
            gap: 8rpx;
            margin-bottom: 12rpx;

            .badge {
              display: flex;
              align-items: center;
              gap: 4rpx;
              padding: 4rpx 10rpx;
              border-radius: 12rpx;
              font-size: 20rpx;
              font-weight: 500;

              &.own-badge {
                background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
                color: #fff;
                box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.3);
              }

              &.type-badge {
                background: #f0f0f0;
                color: #666;

                &.service-type {
                  background: rgba(52, 199, 89, 0.1);
                  color: #34c759;
                }

                &.product-type {
                  background: rgba(255, 149, 0, 0.1);
                  color: #ff9500;
                }

                &.single-type {
                  background: rgba(0, 122, 255, 0.1);
                  color: #007AFF;
                }

                &.package-type {
                  background: rgba(255, 59, 48, 0.1);
                  color: #ff3b30;
                }
              }
            }
          }
        }

        .product-category {
          display: flex;
          align-items: center;
          gap: 8rpx;
          margin-bottom: 16rpx;

          .category-text {
            font-size: 28rpx;
            color: #fdd118;
            font-weight: 600;
          }

          .main-category-text {
            font-size: 26rpx;
            color: #999;
          }
        }

        .product-description {
          margin-bottom: 12rpx;

          .description-text {
            font-size: 24rpx;
            color: #666;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }

        .product-specs {
          margin-bottom: 20rpx;

          .spec-item {
            display: flex;
            align-items: center;
            margin-bottom: 8rpx;

            .spec-text {
              font-size: 26rpx;
              color: #666;
              font-weight: 500;
            }
          }
        }

        // SKU选项区域
        .product-skus {
          margin-top: 24rpx;

          .sku-title {
            margin-bottom: 16rpx;

            text {
              font-size: 26rpx;
              color: #333;
              font-weight: 500;
            }
          }

          .sku-list {
            display: flex;
            flex-direction: column;
            gap: 12rpx;

            .sku-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 16rpx 20rpx;
              background: #f8f9fa;
              border-radius: 16rpx;
              border: 2rpx solid transparent;
              transition: all 0.3s ease;

              &:active {
                background: #e9ecef;
                border-color: #fdd118;
              }

              .sku-info {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .sku-name {
                  font-size: 26rpx;
                  color: #333;
                  font-weight: 500;
                }

                .sku-price {
                  font-size: 28rpx;
                  color: #ff6b35;
                  font-weight: 600;
                }
              }

              .sku-select-btn {
                width: 48rpx;
                height: 48rpx;
                border-radius: 24rpx;
                background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 16rpx;
              }
            }
          }
        }

        // 无SKU提示
        .no-sku-tip {
          margin-top: 24rpx;
          padding: 20rpx;
          text-align: center;
          background: #f8f9fa;
          border-radius: 16rpx;

          text {
            font-size: 24rpx;
            color: #999;
          }
        }
      }
    }
  }
}

// 加载更多样式
.load-more, .loading-more, .no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 20rpx;
  gap: 12rpx;

  .load-more-text, .loading-more-text, .no-more-text {
    font-size: 26rpx;
    color: #666;
  }
}

.load-more {
  &:active {
    opacity: 0.7;
  }
}

.no-more {
  .no-more-text {
    color: #999;
  }
}
</style>
