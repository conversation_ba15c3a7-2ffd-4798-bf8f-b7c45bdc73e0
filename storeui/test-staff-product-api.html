<!DOCTYPE html>
<html>
<head>
    <title>员工端产品API测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>员工端产品API数据格式测试</h1>
    
    <div class="test-section">
        <h2>测试场景</h2>
        <p>验证员工端产品列表页面的数据格式转换是否正确</p>
        
        <h3>原始API返回格式 (员工端)</h3>
        <pre id="original-format"></pre>
        
        <h3>转换后格式 (兼容门店端)</h3>
        <pre id="converted-format"></pre>
        
        <h3>测试结果</h3>
        <div id="test-results"></div>
    </div>

    <script>
        // 模拟员工端API返回的数据格式
        const mockStaffApiResponse = {
            "categories": [
                {
                    "name": "家电清洗",
                    "products": [
                        {
                            "id": 2028,
                            "product_name": "油烟机(免拆)清洗",
                            "name": "油烟机(免拆)清洗",
                            "service_skill_name": "家电清洗",
                            "service_skill_main_name": "家电清洗",
                            "img_id": null,
                            "product_status": 0,
                            "details": "专业油烟机清洗服务",
                            "uuid": "product_2028",
                            "sku_info": {
                                "now_price": "129",
                                "type_price_unit": "/次",
                                "vip_price": "119"
                            }
                        }
                    ]
                },
                {
                    "name": "居家保洁",
                    "products": [
                        {
                            "id": 2030,
                            "product_name": "单次居家保洁",
                            "service_skill_name": "居家保洁",
                            "service_skill_main_name": "居家保洁",
                            "sku_info": {
                                "now_price": "199",
                                "type_price_unit": "/次",
                                "vip_price": "189"
                            }
                        }
                    ]
                }
            ],
            "total_products": 2,
            "total_categories": 2,
            "company_info": {
                "company_id": "test_company",
                "store_name": "测试门店"
            }
        };

        // 显示原始格式
        document.getElementById('original-format').textContent = JSON.stringify(mockStaffApiResponse, null, 2);

        // 模拟数据转换逻辑
        function convertStaffApiData(response) {
            if (!response || !response.categories) {
                throw new Error('员工端API返回数据格式错误');
            }

            const products = [];
            const categories = ['全部'];
            
            response.categories.forEach(category => {
                if (category.name && category.name !== '全部') {
                    categories.push(category.name);
                }
                if (category.products && Array.isArray(category.products)) {
                    category.products.forEach(product => {
                        const convertedProduct = {
                            id: product.id,
                            uuid: product.uuid || product.product_uuid || `product_${product.id}`,
                            product_name: product.product_name || product.name,
                            service_skill_name: product.service_skill_name || category.name,
                            service_skill_main_name: product.service_skill_main_name || category.name,
                            type_name: '服务产品',
                            is_own_product: true,
                            cover_image_url: product.img_id || '',
                            images: [],
                            min_number: 1,
                            max_number: 99,
                            buy_notes: product.details || '',
                            buy_agreement: '',
                            skus: []
                        };
                        
                        // 处理SKU信息
                        if (product.sku_info && product.sku_info.now_price) {
                            convertedProduct.skus = [{
                                id: `${product.id}_sku`,
                                name: product.product_name || product.name,
                                now_price: parseFloat(product.sku_info.now_price) || 0,
                                vip_price: parseFloat(product.sku_info.vip_price) || 0,
                                type_price_unit: product.sku_info.type_price_unit || '/次',
                                define_commission: 0,
                                commission_type: 1,
                                duration: 60
                            }];
                        } else {
                            convertedProduct.skus = [{
                                id: `${product.id}_sku`,
                                name: product.product_name || product.name,
                                now_price: 0,
                                vip_price: 0,
                                type_price_unit: '/次',
                                define_commission: 0,
                                commission_type: 1,
                                duration: 60
                            }];
                        }
                        
                        products.push(convertedProduct);
                    });
                }
            });
            
            return {
                products: products,
                categories: categories.map(name => ({ name })),
                total: products.length,
                pagination: {
                    total: products.length,
                    pages: 1,
                    current_page: 1,
                    per_page: products.length
                },
                current_page: 1,
                total_pages: 1
            };
        }

        // 执行转换测试
        try {
            const convertedData = convertStaffApiData(mockStaffApiResponse);
            document.getElementById('converted-format').textContent = JSON.stringify(convertedData, null, 2);
            
            // 验证关键字段
            const results = [];
            
            // 检查产品数量
            if (convertedData.products && convertedData.products.length === 2) {
                results.push('<span class="success">✓ 产品数量正确: 2个产品</span>');
            } else {
                results.push('<span class="error">✗ 产品数量错误</span>');
            }
            
            // 检查分页信息
            if (convertedData.pagination && convertedData.pagination.total === 2) {
                results.push('<span class="success">✓ 分页信息正确: total = 2</span>');
            } else {
                results.push('<span class="error">✗ 分页信息错误</span>');
            }
            
            // 检查SKU信息
            const firstProduct = convertedData.products[0];
            if (firstProduct.skus && firstProduct.skus.length > 0 && firstProduct.skus[0].now_price === 129) {
                results.push('<span class="success">✓ SKU价格转换正确: 129元</span>');
            } else {
                results.push('<span class="error">✗ SKU价格转换错误</span>');
            }
            
            // 检查分类信息
            if (convertedData.categories && convertedData.categories.length === 3) {
                results.push('<span class="success">✓ 分类信息正确: 3个分类(包含"全部")</span>');
            } else {
                results.push('<span class="error">✗ 分类信息错误</span>');
            }
            
            document.getElementById('test-results').innerHTML = results.join('<br>');
            
        } catch (error) {
            document.getElementById('test-results').innerHTML = `<span class="error">转换失败: ${error.message}</span>`;
        }
    </script>
</body>
</html>
