"""
员工端订单服务层
"""
import logging
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.dao.staff_order_dao import StaffOrderDao
from module_admin.service.file_service import FileService
from utils.log_util import logger
from exceptions.exception import QueryException, BusinessException

logger = logging.getLogger(__name__)


class StaffOrderService:
    """员工端订单服务类"""

    @classmethod
    async def get_staff_order_list_service(
        cls,
        query_db: AsyncSession,
        current_staff,  # 改为传入完整的员工对象
        page: int,
        size: int,
        status: Optional[str] = None,
        keyword: Optional[str] = None,
        store_id: Optional[str] = None,
        store_uuid: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取员工订单列表服务

        :param query_db: 数据库会话
        :param current_staff: 当前登录员工对象
        :param page: 页码
        :param size: 每页数量
        :param status: 订单状态筛选
        :param keyword: 关键词搜索
        :param store_id: 门店ID（必传）
        :param store_uuid: 门店UUID（必传）
        :return: 订单列表数据
        """
        try:
            logger.info(f"开始获取员工订单列表，员工手机号: {current_staff.mobile}, 页码: {page}, 每页: {size}, 门店ID: {store_id}")

            # 根据员工手机号和门店信息找到对应的员工ID
            from module_admin.dao.service_staff_dao import ServiceStaffDao
            staff_list = await ServiceStaffDao.get_staff_by_mobile(query_db, current_staff.mobile)

            # 找到指定门店的员工记录
            target_staff = None
            for staff in staff_list:
                if staff.store_id == store_id and staff.store_uuid == store_uuid:
                    target_staff = staff
                    break

            if not target_staff:
                logger.warning(f"未找到员工在门店{store_id}的记录，手机号: {current_staff.mobile}")
                # 如果找不到对应门店的员工记录，返回空列表
                return {
                    'list': [],
                    'total': 0,
                    'page': page,
                    'size': size,
                    'has_more': False
                }

            actual_staff_id = target_staff.id
            logger.info(f"找到对应门店的员工ID: {actual_staff_id}, 门店: {target_staff.store_name}")

            # 调用DAO层获取订单列表
            order_list, total = await StaffOrderDao.get_staff_order_list(
                query_db, actual_staff_id, page, size, status, keyword, store_id, store_uuid
            )
            
            # 获取统计信息
            stats = await StaffOrderDao.get_staff_order_statistics(query_db, actual_staff_id)

            # 构建返回数据
            result = {
                "list": order_list,
                "total": total,
                "page": page,
                "size": size,
                "stat": stats
            }

            logger.info(f"员工订单列表获取成功，员工ID: {actual_staff_id}, 返回 {len(order_list)} 条记录")
            return result
            
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取员工订单列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取员工订单列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取员工订单列表失败: {str(e)}")

    @classmethod
    async def get_staff_order_statistics_service(
        cls,
        query_db: AsyncSession,
        staff_id: int
    ) -> Dict[str, Any]:
        """
        获取员工订单统计信息服务
        
        :param query_db: 数据库会话
        :param staff_id: 员工ID
        :return: 统计信息
        """
        try:
            logger.info(f"开始获取员工订单统计，员工ID: {staff_id}")
            
            # 调用DAO层获取统计信息
            stats = await StaffOrderDao.get_staff_order_statistics(query_db, staff_id)
            
            logger.info(f"员工订单统计获取成功，员工ID: {staff_id}")
            return stats
            
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取员工订单统计查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取员工订单统计服务异常: {str(e)}")
            raise BusinessException(message=f"获取员工订单统计失败: {str(e)}")

    @classmethod
    async def get_commission_statistics_service(
        cls,
        query_db: AsyncSession,
        staff_id: int,
        store_uuid: str = None
    ) -> Dict[str, Any]:
        """
        获取员工提成统计信息服务

        :param query_db: 数据库会话
        :param staff_id: 员工ID
        :param store_uuid: 门店UUID（可选）
        :return: 提成统计信息
        """
        try:
            logger.info(f"开始获取员工提成统计，员工ID: {staff_id}, 门店UUID: {store_uuid}")

            # 调用DAO层获取提成统计信息
            commission_stats = await StaffOrderDao.get_staff_commission_statistics(
                query_db, staff_id, store_uuid
            )

            logger.info(f"员工提成统计获取成功，员工ID: {staff_id}")
            return commission_stats

        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取员工提成统计查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取员工提成统计服务异常: {str(e)}")
            raise BusinessException(message=f"获取员工提成统计失败: {str(e)}")

    @classmethod
    async def accept_order_service(
        cls,
        query_db: AsyncSession,
        staff_id: int,
        order_number: str
    ) -> Dict[str, Any]:
        """
        员工确认接单服务

        :param query_db: 数据库会话
        :param staff_id: 员工ID
        :param order_number: 订单编号
        :return: 操作结果
        """
        try:
            logger.info(f"开始处理员工接单，员工ID: {staff_id}, 订单号: {order_number}")

            # 验证员工是否有权限操作该订单
            has_permission = await StaffOrderDao.verify_staff_order_permission(
                query_db, staff_id, order_number
            )

            if not has_permission:
                raise BusinessException(message="您没有权限操作此订单")

            # 验证订单当前状态是否为20（派单待确认）
            current_status = await StaffOrderDao.get_order_status(query_db, order_number)
            if current_status != 20:
                if current_status == 40:
                    raise BusinessException(message="订单已被接单，无法重复操作")
                elif current_status == 30:
                    raise BusinessException(message="订单已被拒绝，无法接单")
                else:
                    raise BusinessException(message="订单状态不正确，无法接单")

            # 更新订单状态为40（已派单）
            success = await StaffOrderDao.update_order_status(
                query_db, order_number, 40, "已派单"
            )

            if not success:
                raise BusinessException(message="更新订单状态失败")

            # 提交事务
            await query_db.commit()

            logger.info(f"员工接单成功，员工ID: {staff_id}, 订单号: {order_number}")
            return {
                "order_number": order_number,
                "status": 40,
                "status_name": "已派单",
                "message": "接单成功"
            }

        except BusinessException as e:
            # 业务异常，回滚事务并向上传递
            await query_db.rollback()
            logger.error(f"员工接单业务异常: {e.message}")
            raise e
        except QueryException as e:
            # 查询异常，回滚事务并向上传递
            await query_db.rollback()
            logger.error(f"员工接单查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，回滚事务并包装为业务异常
            await query_db.rollback()
            logger.error(f"员工接单服务异常: {str(e)}")
            raise BusinessException(message=f"接单失败: {str(e)}")

    @classmethod
    async def reject_order_service(
        cls,
        query_db: AsyncSession,
        staff_id: int,
        order_number: str,
        reject_reason: str = ""
    ) -> Dict[str, Any]:
        """
        员工拒绝接单服务

        :param query_db: 数据库会话
        :param staff_id: 员工ID
        :param order_number: 订单编号
        :param reject_reason: 拒绝原因
        :return: 操作结果
        """
        try:
            logger.info(f"开始处理员工拒绝接单，员工ID: {staff_id}, 订单号: {order_number}")

            # 验证员工是否有权限操作该订单
            has_permission = await StaffOrderDao.verify_staff_order_permission(
                query_db, staff_id, order_number
            )

            if not has_permission:
                raise BusinessException(message="您没有权限操作此订单")

            # 验证订单当前状态是否为20（派单待确认）
            current_status = await StaffOrderDao.get_order_status(query_db, order_number)
            if current_status != 20:
                if current_status == 40:
                    raise BusinessException(message="订单已被接单，无法拒绝")
                elif current_status == 30:
                    raise BusinessException(message="订单已被拒绝，无法重复操作")
                else:
                    raise BusinessException(message="订单状态不正确，无法拒绝")

            # 更新订单状态为30（拒绝接单）
            success = await StaffOrderDao.update_order_status(
                query_db, order_number, 30, "拒绝接单"
            )

            if not success:
                raise BusinessException(message="更新订单状态失败")

            # 如果有拒绝原因，记录到订单备注中
            if reject_reason:
                await StaffOrderDao.update_order_remark(
                    query_db, order_number, f"拒绝原因：{reject_reason}"
                )

            # 提交事务
            await query_db.commit()

            logger.info(f"员工拒绝接单成功，员工ID: {staff_id}, 订单号: {order_number}")
            return {
                "order_number": order_number,
                "status": 30,
                "status_name": "拒绝接单",
                "reject_reason": reject_reason,
                "message": "拒绝接单成功"
            }

        except BusinessException as e:
            # 业务异常，回滚事务并向上传递
            await query_db.rollback()
            logger.error(f"员工拒绝接单业务异常: {e.message}")
            raise e
        except QueryException as e:
            # 查询异常，回滚事务并向上传递
            await query_db.rollback()
            logger.error(f"员工拒绝接单查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，回滚事务并包装为业务异常
            await query_db.rollback()
            logger.error(f"员工拒绝接单服务异常: {str(e)}")
            raise BusinessException(message=f"拒绝接单失败: {str(e)}")

    @classmethod
    async def update_order_status_service(
        cls,
        query_db: AsyncSession,
        staff_id: int,
        order_number: str,
        new_status: int
    ) -> Dict[str, Any]:
        """
        员工更新订单状态服务

        :param query_db: 数据库会话
        :param staff_id: 员工ID
        :param order_number: 订单编号
        :param new_status: 新状态
        :return: 操作结果
        """
        try:
            logger.info(f"开始处理员工更新订单状态，员工ID: {staff_id}, 订单号: {order_number}, 新状态: {new_status}")

            # 验证员工是否有权限操作该订单
            has_permission = await StaffOrderDao.verify_staff_order_permission(
                query_db, staff_id, order_number
            )

            if not has_permission:
                raise BusinessException(message="您没有权限操作此订单")

            # 获取订单当前状态
            current_status = await StaffOrderDao.get_order_status(query_db, order_number)
            if current_status is None:
                raise BusinessException(message="订单不存在")

            # 验证状态流转是否合法
            valid_transitions = {
                40: [50],  # 已派单 → 执行中
                50: [60],  # 执行中 → 开始服务
                60: [80],  # 开始服务 → 已完成
                70: [80],  # 服务结束 → 已完成（兼容旧流程）
            }

            if current_status not in valid_transitions or new_status not in valid_transitions[current_status]:
                status_names = {
                    40: "已派单", 50: "执行中", 60: "开始服务",
                    70: "服务结束", 80: "已完成"
                }
                current_name = status_names.get(current_status, "未知状态")
                new_name = status_names.get(new_status, "未知状态")
                raise BusinessException(message=f"订单状态不能从{current_name}直接更新为{new_name}")

            # 获取状态名称
            status_names = {
                40: "已派单", 50: "执行中", 60: "开始服务",
                70: "服务结束", 80: "已完成"
            }
            status_name = status_names.get(new_status, "未知状态")

            # 更新订单状态
            success = await StaffOrderDao.update_order_status(
                query_db, order_number, new_status, status_name
            )

            if not success:
                raise BusinessException(message="更新订单状态失败")

            # 提交事务
            await query_db.commit()

            logger.info(f"员工更新订单状态成功，员工ID: {staff_id}, 订单号: {order_number}, 新状态: {new_status}")
            return {
                "order_number": order_number,
                "old_status": current_status,
                "new_status": new_status,
                "status_name": status_name,
                "message": "状态更新成功"
            }

        except BusinessException as e:
            # 业务异常，回滚事务并向上传递
            await query_db.rollback()
            logger.error(f"员工更新订单状态业务异常: {e.message}")
            raise e
        except QueryException as e:
            # 查询异常，回滚事务并向上传递
            await query_db.rollback()
            logger.error(f"员工更新订单状态查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，回滚事务并包装为业务异常
            await query_db.rollback()
            logger.error(f"员工更新订单状态服务异常: {str(e)}")
            raise BusinessException(message=f"状态更新失败: {str(e)}")

    @classmethod
    async def start_service_service(
        cls,
        query_db: AsyncSession,
        staff_id: str,
        order_number: str,
        files: list
    ) -> Dict:
        """
        员工开始服务服务

        :param query_db: 数据库会话
        :param staff_id: 员工ID
        :param order_number: 订单编号
        :param files: 环境图文件列表
        :return: 操作结果
        """
        try:
            logger.info(f"开始处理员工开始服务，员工ID: {staff_id}, 订单号: {order_number}")

            # 验证员工是否有权限操作该订单
            has_permission = await StaffOrderDao.verify_staff_order_permission(
                query_db, staff_id, order_number
            )

            if not has_permission:
                raise BusinessException(message="您没有权限操作此订单")

            # 获取订单当前状态
            current_status = await StaffOrderDao.get_order_status(query_db, order_number)
            if current_status is None:
                raise BusinessException(message="订单不存在")

            # 验证状态流转是否合法（50执行中 → 60开始服务）
            if current_status != 50:
                raise BusinessException(message="只有执行中的订单才能开始服务")

            # 更新订单状态为开始服务
            success = await StaffOrderDao.update_order_status(
                query_db, order_number, 60, "开始服务"
            )

            if not success:
                raise BusinessException(message="更新订单状态失败")

            # 处理环境图文件组
            env_before_file_id = None
            if files and len(files) > 0:
                # 创建文件组
                env_before_file_id = await FileService.create_file_group_from_upload_results(
                    query_db, files, "开始服务环境图", str(staff_id)
                )

                if not env_before_file_id:
                    raise BusinessException(message="创建环境图文件组失败")

            # 更新order_waiter表的服务开始时间和环境图文件组ID
            await StaffOrderDao.update_service_start_info(
                query_db, order_number, env_before_file_id
            )

            # 提交事务
            await query_db.commit()

            logger.info(f"员工开始服务成功，员工ID: {staff_id}, 订单号: {order_number}")
            return {
                "order_number": order_number,
                "status": 60,
                "status_name": "开始服务",
                "env_before_file_id": env_before_file_id,
                "message": "开始服务成功"
            }

        except BusinessException as e:
            await query_db.rollback()
            logger.error(f"员工开始服务业务异常: {e.message}")
            raise e
        except QueryException as e:
            await query_db.rollback()
            logger.error(f"员工开始服务查询异常: {e.message}")
            raise e
        except Exception as e:
            await query_db.rollback()
            logger.error(f"员工开始服务异常: {str(e)}")
            raise BusinessException(message=f"开始服务失败: {str(e)}")

    @classmethod
    async def upload_service_before_images_service(
        cls,
        query_db: AsyncSession,
        staff_id: str,
        order_number: str,
        service_before_file_id: str
    ) -> Dict:
        """
        上传服务前图片服务

        :param query_db: 数据库会话
        :param staff_id: 员工ID
        :param order_number: 订单编号
        :param service_before_file_id: 服务前图片文件ID
        :return: 操作结果
        """
        try:
            logger.info(f"开始处理上传服务前图片，员工ID: {staff_id}, 订单号: {order_number}")

            # 验证员工是否有权限操作该订单
            has_permission = await StaffOrderDao.verify_staff_order_permission(
                query_db, staff_id, order_number
            )

            if not has_permission:
                raise BusinessException(message="您没有权限操作此订单")

            # 更新order_waiter表的服务前图片
            await StaffOrderDao.update_service_before_images(
                query_db, order_number, service_before_file_id
            )

            # 提交事务
            await query_db.commit()

            logger.info(f"上传服务前图片成功，员工ID: {staff_id}, 订单号: {order_number}")
            return {
                "order_number": order_number,
                "service_before_file_id": service_before_file_id,
                "message": "服务前图片上传成功"
            }

        except BusinessException as e:
            await query_db.rollback()
            logger.error(f"上传服务前图片业务异常: {e.message}")
            raise e
        except QueryException as e:
            await query_db.rollback()
            logger.error(f"上传服务前图片查询异常: {e.message}")
            raise e
        except Exception as e:
            await query_db.rollback()
            logger.error(f"上传服务前图片异常: {str(e)}")
            raise BusinessException(message=f"服务前图片上传失败: {str(e)}")

    @classmethod
    async def end_service_service(
        cls,
        query_db: AsyncSession,
        staff_id: str,
        order_number: str,
        service_after_file_id: Optional[str],
        signature_file_id: Optional[str]
    ) -> Dict:
        """
        员工结束服务服务

        :param query_db: 数据库会话
        :param staff_id: 员工ID
        :param order_number: 订单编号
        :param service_after_file_id: 服务后图片文件ID
        :param signature_file_id: 电子签名文件ID
        :return: 操作结果
        """
        try:
            logger.info(f"开始处理员工结束服务，员工ID: {staff_id}, 订单号: {order_number}")

            # 验证员工是否有权限操作该订单
            has_permission = await StaffOrderDao.verify_staff_order_permission(
                query_db, staff_id, order_number
            )

            if not has_permission:
                raise BusinessException(message="您没有权限操作此订单")

            # 获取订单当前状态
            current_status = await StaffOrderDao.get_order_status(query_db, order_number)
            if current_status is None:
                raise BusinessException(message="订单不存在")

            # 验证状态流转是否合法（60开始服务 → 80已完成）
            if current_status != 60:
                raise BusinessException(message="只有开始服务的订单才能结束服务")

            # 更新订单状态为已完成
            success = await StaffOrderDao.update_order_status(
                query_db, order_number, 80, "已完成"
            )

            if not success:
                raise BusinessException(message="更新订单状态失败")

            # 更新order_waiter表的服务结束时间、服务后图片和电子签名
            await StaffOrderDao.update_service_end_info(
                query_db, order_number, service_after_file_id, signature_file_id
            )

            # 处理共享订单的佣金分配
            await cls._process_shared_order_commission(query_db, order_number)

            # 提交事务
            await query_db.commit()

            logger.info(f"员工结束服务成功，员工ID: {staff_id}, 订单号: {order_number}")
            return {
                "order_number": order_number,
                "status": 80,
                "status_name": "已完成",
                "service_after_file_id": service_after_file_id,
                "signature_file_id": signature_file_id,
                "message": "结束服务成功"
            }

        except BusinessException as e:
            await query_db.rollback()
            logger.error(f"员工结束服务业务异常: {e.message}")
            raise e
        except QueryException as e:
            await query_db.rollback()
            logger.error(f"员工结束服务查询异常: {e.message}")
            raise e
        except Exception as e:
            await query_db.rollback()
            logger.error(f"员工结束服务异常: {str(e)}")
            raise BusinessException(message=f"结束服务失败: {str(e)}")

    @classmethod
    async def _process_shared_order_commission(
        cls,
        query_db: AsyncSession,
        order_number: str
    ):
        """
        处理共享订单完成后的佣金分配
        """
        try:
            from sqlalchemy import text

            # 1. 检查是否为共享订单
            shared_order_query = text("""
                SELECT
                    o.id as order_id,
                    o.order_number,
                    o.store_id as original_store_id,
                    o.store_uuid as original_store_uuid,
                    o.store_name as original_store_name,
                    o.pay_actual as order_amount,
                    ds.commission_amount,
                    ds.grab_store_uuid as grab_store_uuid,
                    ds.grab_store_name as grab_store_name,
                    ow.service_id as staff_id,
                    ow.service_name as staff_name,
                    ow.service_personal_commission,
                    s_original.company_id as original_company_id,
                    s_grab.company_id as grab_company_id
                FROM `order` o
                LEFT JOIN demand_square ds ON o.id = ds.source_order_id
                LEFT JOIN order_waiter ow ON o.order_number = ow.order_number
                LEFT JOIN store s_original ON o.store_id = s_original.id
                LEFT JOIN store s_grab ON ds.grab_store_uuid = s_grab.store_uuid
                WHERE o.order_number = :order_number
                  AND ds.source_order_id IS NOT NULL
                LIMIT 1
            """)

            result = await query_db.execute(shared_order_query, {"order_number": order_number})
            shared_order = result.fetchone()

            if not shared_order:
                # 不是共享订单，无需处理
                logger.info(f"订单 {order_number} 不是共享订单，跳过佣金分配")
                return

            commission_amount = float(shared_order.commission_amount or 0)
            if commission_amount <= 0:
                logger.info(f"共享订单 {order_number} 佣金金额为0，跳过佣金分配")
                return

            logger.info(f"开始处理共享订单佣金分配: 订单={order_number}, 佣金={commission_amount}元")

            # 2. 给接单门店的员工分配佣金（通过finance_transaction表）
            await cls._create_staff_commission_transaction(
                query_db, shared_order, commission_amount
            )

            logger.info(f"共享订单佣金分配完成: 订单={order_number}，已为接单员工创建佣金收入记录")

        except Exception as e:
            logger.error(f"处理共享订单佣金分配失败: {str(e)}")
            # 不抛出异常，避免影响订单完成流程
            pass

    @classmethod
    async def _create_staff_commission_transaction(
        cls,
        query_db: AsyncSession,
        shared_order,
        commission_amount: float
    ):
        """
        为接单门店的员工创建佣金收入流水
        """
        try:
            from sqlalchemy import text
            import uuid
            import time

            # 生成流水号
            transaction_uuid = str(uuid.uuid4())

            # 查询员工信息
            staff_query = text("""
                SELECT user_id, store_uuid
                FROM service_staff
                WHERE id = :staff_id
                LIMIT 1
            """)
            staff_result = await query_db.execute(staff_query, {"staff_id": shared_order.staff_id})
            staff_info = staff_result.fetchone()

            if not staff_info:
                logger.warning(f"未找到员工信息: staff_id={shared_order.staff_id}，可能员工已被删除，跳过员工佣金分配")
                return

            # 插入员工佣金收入流水
            insert_transaction = text("""
                INSERT INTO finance_transaction (
                    uuid, store_uuid, user_id, create_time, op_user_name,
                    type_name, pay_user_type_name, op_sub_type, op_money,
                    account_type_name, pay_type_name, curr_balance, op_info,
                    pay_remark, income_pay_type, account_type
                ) VALUES (
                    :uuid, :store_uuid, :user_id, NOW(), :op_user_name,
                    '收入', '员工', '共享订单佣金', :op_money,
                    '现金账户', '佣金收入', '0', :op_info,
                    :pay_remark, 'COMMISSION', 'CASH'
                )
            """)

            op_info = f"共享订单佣金收入，订单号：{shared_order.order_number}，佣金金额：{commission_amount}元"
            pay_remark = f"接单门店：{shared_order.grab_store_name}，服务员工：{shared_order.staff_name}"

            await query_db.execute(insert_transaction, {
                "uuid": transaction_uuid,
                "store_uuid": staff_info.store_uuid,
                "user_id": staff_info.user_id,
                "op_user_name": shared_order.staff_name,
                "op_money": str(commission_amount),
                "op_info": op_info,
                "pay_remark": pay_remark
            })

            logger.info(f"员工佣金收入流水创建成功: 员工={shared_order.staff_name}, 金额={commission_amount}元")

        except Exception as e:
            logger.error(f"创建员工佣金收入流水失败: {str(e)}")
            raise

    @classmethod
    async def create_staff_proxy_order_service(
        cls,
        query_db: AsyncSession,
        order_data,
        current_staff
    ) -> Dict[str, Any]:
        """
        员工代客下单服务

        :param query_db: 数据库会话
        :param order_data: 订单数据
        :param current_staff: 当前员工信息
        :return: 创建结果
        """
        try:
            logger.info(f"开始处理员工代客下单，员工ID: {current_staff.id}")

            # 1. 验证员工权限和门店关联
            staff_store_info = await cls._validate_staff_store_permission(query_db, current_staff)

            # 2. 验证产品和SKU信息
            product_info, sku_info = await cls._validate_product_and_sku(
                query_db, order_data.product_info, staff_store_info['store_uuid']
            )

            # 3. 处理客户信息和地址
            customer_info, address_id = await cls._handle_customer_and_address(
                query_db, order_data.customer_info, order_data.address_info, staff_store_info
            )

            # 4. 生成员工代客下单数据
            order_dict = await cls._build_staff_proxy_order_data(
                order_data, staff_store_info, product_info, sku_info, current_staff, customer_info, address_id
            )

            # 5. 创建订单记录
            order_result = await cls._create_staff_proxy_order_record(query_db, order_dict)

            # 6. 创建相关记录（地址、备注等）
            await cls._create_staff_proxy_related_records(query_db, order_result['order_id'], order_data)

            # 7. 提交事务
            await query_db.commit()

            logger.info(f"员工代客下单创建成功，订单号: {order_result['order_number']}")

            return {
                "order_id": order_result['order_id'],
                "order_number": order_result['order_number'],
                "order_status": 10,  # 已接单
                "order_status_name": "已接单",
                "total_amount": order_data.order_info.amount,
                "product_name": product_info.get('product_name', '家政服务'),
                "create_time": order_result['create_time'],
                "operator_type": "staff",  # 标识为员工操作
                "operator_name": current_staff.real_name or current_staff.name or "员工"
            }

        except Exception as e:
            await query_db.rollback()
            logger.error(f"员工代客下单失败: {str(e)}")
            raise BusinessException(message=f"员工代客下单失败: {str(e)}", code=1007)

    @classmethod
    async def _validate_staff_store_permission(cls, query_db: AsyncSession, current_staff) -> Dict[str, Any]:
        """验证员工权限和门店关联"""
        try:
            # 获取员工关联的门店信息
            from module_admin.dao.service_staff_dao import ServiceStaffDao
            staff_stores = await ServiceStaffDao.get_staff_stores(query_db, current_staff.id)

            if not staff_stores:
                raise BusinessException(message="员工未关联任何门店，无法代客下单", code=1006)

            # 使用第一个关联的门店（实际项目中可能需要让员工选择）
            staff_store = staff_stores[0]

            return {
                'store_id': staff_store.get('store_id'),
                'store_uuid': staff_store.get('store_uuid'),
                'store_name': staff_store.get('store_name', ''),
                'staff_id': current_staff.id,
                'staff_uuid': getattr(current_staff, 'uuid', None),
                'staff_name': current_staff.real_name or current_staff.name or "员工"
            }

        except Exception as e:
            logger.error(f"验证员工门店权限失败: {str(e)}")
            raise BusinessException(message="验证员工权限失败", code=1006)

    @classmethod
    async def _validate_product_and_sku(cls, query_db: AsyncSession, product_info, store_uuid: str) -> tuple:
        """验证产品和SKU信息"""
        try:
            # 这里应该调用产品服务验证产品和SKU
            # 暂时返回基本信息，实际项目中需要实现具体的验证逻辑
            product_data = {
                'product_id': product_info.product_uuid,
                'product_uuid': product_info.product_uuid,
                'product_name': product_info.sku_name or '家政服务',
                'product_type': 1,
                'service_type': 1
            }

            sku_data = {
                'sku_id': product_info.sku_id,
                'sku_name': product_info.sku_name,
                'sku_price': product_info.sku_price,
                'sku_commission': product_info.sku_commission,
                'sku_commission_type': product_info.sku_commission_type
            }

            return product_data, sku_data

        except Exception as e:
            logger.error(f"验证产品SKU信息失败: {str(e)}")
            raise BusinessException(message="产品信息验证失败", code=1004)

    @classmethod
    async def _handle_customer_and_address(cls, query_db: AsyncSession, customer_info, address_info, staff_store_info) -> tuple:
        """处理客户信息和地址"""
        try:
            # 这里应该调用客户服务处理客户信息
            # 暂时返回基本信息，实际项目中需要实现具体的客户处理逻辑
            customer_data = {
                'name': customer_info.name,
                'phone': customer_info.phone,
                'original_input': customer_info.original_input or ''
            }

            # 地址ID暂时设为0，实际项目中需要创建地址记录
            address_id = 0

            return customer_data, address_id

        except Exception as e:
            logger.error(f"处理客户信息和地址失败: {str(e)}")
            raise BusinessException(message="客户信息处理失败", code=1002)

    @classmethod
    async def _build_staff_proxy_order_data(
        cls,
        order_data,
        staff_store_info: Dict[str, Any],
        product_info: Dict[str, Any],
        sku_info: Dict[str, Any],
        current_staff,
        customer_info: Dict[str, Any],
        address_id: int
    ) -> Dict[str, Any]:
        """构建员工代客下单数据"""
        try:
            import datetime
            import uuid

            # 拼接服务日期时间
            service_datetime = f"{order_data.order_info.service_date} {order_data.order_info.service_time}"

            # 构建完整的服务地址
            full_address = order_data.address_info.address
            if order_data.address_info.door_number:
                full_address += f" {order_data.address_info.door_number}"

            return {
                # 基础订单信息
                "id": str(uuid.uuid4()),
                "order_number": cls._generate_staff_proxy_order_number(),
                "user_name": customer_info['name'],
                "user_phone": customer_info['phone'],
                "store_id": staff_store_info['store_id'],
                "store_uuid": staff_store_info['store_uuid'],
                "store_name": staff_store_info['store_name'],

                # 产品信息
                "product_id": product_info['product_uuid'],
                "product_name": product_info['product_name'],
                "product_type": product_info.get('product_type', 1),
                "service_type": product_info.get('service_type', 1),
                "product_sku_id": sku_info['sku_id'],
                "service_type_name": sku_info.get('sku_name', ''),

                # 服务信息
                "service_address": full_address,
                "service_date": str(order_data.order_info.service_date),
                "service_time": str(order_data.order_info.service_time),
                "service_phone": customer_info['phone'],
                "service_remark": order_data.remark_info.service_notice if order_data.remark_info else "",

                # 金额信息
                "amount": float(order_data.order_info.amount),
                "buy_num": float(order_data.order_info.buy_num),
                "pay_total": float(order_data.order_info.amount),
                "pay_actual": float(order_data.order_info.amount),
                "total_pay_actual": float(order_data.order_info.amount),
                "pay_method": order_data.order_info.pay_type,
                "pay_status": 1,  # 现金支付默认已支付

                # 地址信息
                "address_id": address_id,
                "longitude": float(order_data.address_info.longitude),
                "latitude": float(order_data.address_info.latitude),
                "bd_province_name": order_data.address_info.province or "",
                "bd_area_name": order_data.address_info.district or "",

                # 扩展业务信息
                "sales_attribution": order_data.business_info.sales_attribution if order_data.business_info else "",
                "sales_attribution_staff_id": getattr(order_data.business_info, 'sales_attribution_staff_id', '') if order_data.business_info else "",
                "sales_commission": float(order_data.business_info.sales_commission) if order_data.business_info and order_data.business_info.sales_commission else 0,
                "channel_code": order_data.business_info.channel_code if order_data.business_info else "",
                "order_source": order_data.business_info.order_source if order_data.business_info else "",
                "area": order_data.business_info.area if order_data.business_info else "",
                "house_type": order_data.business_info.house_type if order_data.business_info else "",

                # 员工代客下单特有标识
                "create_by": current_staff.id,  # 使用员工ID
                "create_by_uuid": staff_store_info['staff_uuid'],  # 员工UUID
                "operator_name": f"员工代客下单 - {staff_store_info['staff_name']}",  # 操作员姓名
                "order_source_type": "staff_proxy",  # 员工代客下单标识
                "source": "staff_proxy",  # 订单来源标识
                "order_status": 10,  # 已接单
                "create_time": datetime.datetime.now(),

                # 备注信息
                "remark": customer_info.get('original_input', ''),
                "customer_note": order_data.remark_info.customer_note if order_data.remark_info else "",
                "after_sale_remark": order_data.remark_info.after_sales_note if order_data.remark_info else ""
            }
        except Exception as e:
            logger.error(f"构建员工代客下单数据失败: {str(e)}")
            raise BusinessException(message="订单数据构建失败", code=1009)

    @classmethod
    def _generate_staff_proxy_order_number(cls) -> str:
        """生成员工代客下单订单号"""
        import time
        import random
        timestamp = int(time.time())
        random_num = random.randint(100000, 999999)
        return f"SPX{timestamp}{random_num}"  # SPX = Staff Proxy

    @classmethod
    async def _create_staff_proxy_order_record(cls, query_db: AsyncSession, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建员工代客下单记录"""
        try:
            from module_admin.dao.order_dao import OrderDao

            # 创建订单记录
            order_id = await OrderDao.create_order(query_db, order_data)

            return {
                "order_id": order_id,
                "order_number": order_data['order_number'],
                "create_time": order_data['create_time']
            }

        except Exception as e:
            logger.error(f"创建员工代客下单记录失败: {str(e)}")
            raise BusinessException(message="订单创建失败", code=1007)

    @classmethod
    async def _create_staff_proxy_related_records(cls, query_db: AsyncSession, order_id: str, order_data) -> None:
        """创建员工代客下单相关记录"""
        try:
            # 这里可以创建订单相关的其他记录，如：
            # 1. 订单地址记录
            # 2. 订单备注记录
            # 3. 订单扩展信息记录
            # 暂时不实现具体逻辑，实际项目中根据需要添加

            logger.info(f"员工代客下单相关记录创建完成，订单ID: {order_id}")

        except Exception as e:
            logger.error(f"创建员工代客下单相关记录失败: {str(e)}")
            raise BusinessException(message="订单相关记录创建失败", code=1008)


